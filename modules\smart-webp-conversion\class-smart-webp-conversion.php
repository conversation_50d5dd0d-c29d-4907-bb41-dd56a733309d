<?php
/**
 * Smart WebP Conversion Module
 *
 * Automatically converts images to WebP format for better performance
 * while maintaining compatibility with all browsers.
 *
 * @package Redco_Optimizer
 * @subpackage Smart_WebP_Conversion
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Smart_WebP_Conversion {

    /**
     * Module version
     */
    const VERSION = '1.0.0';

    /**
     * Module key
     */
    const MODULE_KEY = 'smart-webp-conversion';

    /**
     * Supported image formats for conversion
     */
    const SUPPORTED_FORMATS = ['jpeg', 'jpg', 'png', 'gif'];

    /**
     * Default quality settings
     */
    const DEFAULT_QUALITY = 85;
    const DEFAULT_LOSSLESS = false;

    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
    }

    /**
     * Initialize the module
     */
    public function init() {
        // Only initialize if module is enabled
        if (!redco_is_module_enabled(self::MODULE_KEY)) {
            return;
        }

        // Hook into WordPress upload process
        add_filter('wp_handle_upload', array($this, 'handle_upload_conversion'), 10, 2);
        add_filter('wp_generate_attachment_metadata', array($this, 'generate_webp_versions'), 10, 2);

        // Hook into image serving
        add_filter('wp_get_attachment_image_src', array($this, 'serve_webp_if_supported'), 10, 4);
        add_filter('the_content', array($this, 'replace_images_in_content'), 999);

        // AJAX handlers (all major handlers now handled globally)
        add_action('wp_ajax_redco_webp_rollback', array($this, 'ajax_rollback_conversion'));
        // Debug, bulk_convert, get_stats, and test_conversion AJAX handlers are now registered globally

        // Admin hooks are now handled globally outside the class
    }

    /**
     * Handle new upload conversion
     */
    public function handle_upload_conversion($upload, $context) {
        // Ensure upload file path is valid to prevent wp_is_stream() null parameter warnings
        if (!isset($upload['file']) || empty($upload['file']) || !is_string($upload['file'])) {
            return $upload;
        }

        if (!$this->is_convertible_image($upload['file'])) {
            return $upload;
        }

        $settings = $this->get_settings();
        if (!$settings['auto_convert_uploads']) {
            return $upload;
        }

        try {
            $webp_path = $this->convert_to_webp($upload['file'], $settings);
            if ($webp_path) {
                // Store conversion info in metadata
                $upload['webp_converted'] = true;
                $upload['webp_path'] = $webp_path;
                $upload['original_size'] = filesize($upload['file']);
                $upload['webp_size'] = filesize($webp_path);

                // Log conversion
                $this->log_conversion($upload['file'], $webp_path, 'upload');
            }
        } catch (Exception $e) {
            error_log('Redco WebP Conversion Error: ' . $e->getMessage());
        }

        return $upload;
    }

    /**
     * Generate WebP versions for image sizes
     */
    public function generate_webp_versions($metadata, $attachment_id) {
        // CRITICAL FIX: Ensure metadata is an array to prevent null parameter warnings
        if (!is_array($metadata)) {
            $metadata = array();
        }

        $file_path = get_attached_file($attachment_id);

        // CRITICAL FIX: Ensure file_path is valid to prevent null parameter warnings
        if (!$file_path || !is_string($file_path) || !$this->is_convertible_image($file_path)) {
            return $metadata;
        }

        $settings = $this->get_settings();
        $upload_dir = wp_upload_dir();

        // CRITICAL FIX: Ensure upload_dir has required keys to prevent null parameter warnings
        if (!is_array($upload_dir) || !isset($upload_dir['basedir']) || empty($upload_dir['basedir'])) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ WebP Generation Error: Invalid upload directory configuration');
            }
            return $metadata;
        }

        try {
            // Convert main image
            $webp_path = $this->convert_to_webp($file_path, $settings);
            if ($webp_path && is_string($webp_path) && file_exists($webp_path)) {
                // CRITICAL FIX: Ensure all str_replace parameters are strings
                $basedir = (string) $upload_dir['basedir'];
                $webp_path_str = (string) $webp_path;
                $metadata['webp_versions']['full'] = str_replace($basedir, '', $webp_path_str);
            }

            // Convert image sizes
            if (isset($metadata['sizes']) && is_array($metadata['sizes'])) {
                foreach ($metadata['sizes'] as $size_name => $size_data) {
                    // CRITICAL FIX: Ensure size_data has required keys
                    if (!is_array($size_data) || !isset($size_data['file']) || empty($size_data['file'])) {
                        continue;
                    }

                    $size_file = path_join(dirname($file_path), $size_data['file']);
                    if (file_exists($size_file)) {
                        $size_webp = $this->convert_to_webp($size_file, $settings);
                        if ($size_webp && is_string($size_webp) && file_exists($size_webp)) {
                            // CRITICAL FIX: Ensure all str_replace parameters are strings
                            $basedir = (string) $upload_dir['basedir'];
                            $size_webp_str = (string) $size_webp;
                            $metadata['webp_versions'][$size_name] = str_replace($basedir, '', $size_webp_str);
                        }
                    }
                }
            }

            // Update attachment metadata - CRITICAL FIX: Ensure all values are valid
            $original_size = file_exists($file_path) ? filesize($file_path) : 0;
            $webp_size = (isset($webp_path) && file_exists($webp_path)) ? filesize($webp_path) : 0;

            update_post_meta($attachment_id, '_webp_conversion_data', array(
                'converted' => true,
                'conversion_date' => current_time('mysql'),
                'original_size' => $original_size,
                'webp_size' => $webp_size,
                'quality' => $settings['quality'],
                'lossless' => $settings['lossless']
            ));

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('✅ WebP metadata generation completed for attachment ID: ' . $attachment_id);
            }

        } catch (Exception $e) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ Redco WebP Generation Error: ' . $e->getMessage());
            }
        }

        return $metadata;
    }

    /**
     * Serve WebP images if browser supports it
     */
    public function serve_webp_if_supported($image, $attachment_id, $size, $icon) {
        if (!$this->browser_supports_webp() || !$image) {
            return $image;
        }

        $metadata = wp_get_attachment_metadata($attachment_id);
        if (!isset($metadata['webp_versions'])) {
            return $image;
        }

        $size_key = is_array($size) ? 'full' : $size;
        if (!isset($metadata['webp_versions'][$size_key])) {
            return $image;
        }

        $upload_dir = wp_upload_dir();
        $base_dir = $upload_dir['basedir'] ?? '';
        $base_url = $upload_dir['baseurl'] ?? '';
        $webp_relative = $metadata['webp_versions'][$size_key] ?? '';

        // CRITICAL FIX: Ensure all path components are valid strings
        if (!$base_dir || !$base_url || !$webp_relative) {
            return $image;
        }

        $webp_path = $base_dir . $webp_relative;

        if (file_exists($webp_path)) {
            $webp_url = $base_url . $webp_relative;
            $image[0] = $webp_url;
        }

        return $image;
    }

    /**
     * Replace images in content with WebP versions
     */
    public function replace_images_in_content($content) {
        if (!$this->browser_supports_webp()) {
            return $content;
        }

        $settings = $this->get_settings();
        if (!$settings['replace_in_content']) {
            return $content;
        }

        // Use regex to find and replace image URLs
        $pattern = '/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i';
        return preg_replace_callback($pattern, array($this, 'replace_image_callback'), $content);
    }

    /**
     * Callback for image replacement in content
     */
    private function replace_image_callback($matches) {
        $img_tag = $matches[0];
        $img_url = $matches[1];

        // CRITICAL FIX: Ensure img_url is not null to prevent strpos() deprecation warning
        if (!$img_url || !is_string($img_url) || empty($img_url)) {
            return $img_tag;
        }

        // Check if this is a local image
        $upload_dir = wp_upload_dir();
        $base_url = $upload_dir['baseurl'] ?? '';

        // CRITICAL FIX: Ensure both parameters are strings before strpos()
        if (!$base_url || strpos($img_url, $base_url) === false) {
            return $img_tag;
        }

        // Find corresponding WebP version
        $webp_url = $this->get_webp_url_from_original($img_url);
        if ($webp_url && $webp_url !== $img_url) {
            return str_replace($img_url, $webp_url, $img_tag);
        }

        return $img_tag;
    }

    /**
     * Convert image to WebP format
     */
    private function convert_to_webp($source_path, $settings = null) {
        // CRITICAL FIX: Ensure source_path is valid to prevent all null parameter warnings
        if (!$source_path || !is_string($source_path) || empty($source_path)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ convert_to_webp called with invalid source path: ' . var_export($source_path, true));
            }
            throw new Exception(__('Invalid source path provided for WebP conversion', 'redco-optimizer'));
        }

        // Comprehensive logging for debugging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 WebP Conversion Start - Source: ' . $source_path);
        }

        if (!$this->can_convert_webp()) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ WebP conversion not supported on server');
            }
            throw new Exception(__('WebP conversion not supported on this server', 'redco-optimizer'));
        }

        if ($settings === null) {
            $settings = $this->get_settings();
        }

        // Verify source file exists and is readable
        if (!file_exists($source_path)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ Source file does not exist: ' . $source_path);
            }
            throw new Exception(__('Source image file does not exist', 'redco-optimizer'));
        }

        if (!is_readable($source_path)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ Source file is not readable: ' . $source_path);
            }
            throw new Exception(__('Source image file is not readable', 'redco-optimizer'));
        }

        $webp_path = $this->get_webp_path($source_path);

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 Target WebP path: ' . $webp_path);
        }

        // CRITICAL FIX: Enhanced directory creation and permission handling
        $webp_dir = dirname($webp_path);

        // Ensure directory exists with proper permissions
        if (!file_exists($webp_dir)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🔧 Creating directory: ' . $webp_dir);
            }
            $mkdir_result = wp_mkdir_p($webp_dir);
            if (!$mkdir_result) {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('❌ Failed to create directory: ' . $webp_dir);
                    error_log('❌ Parent directory exists: ' . (is_dir(dirname($webp_dir)) ? 'Yes' : 'No'));
                    error_log('❌ Parent directory writable: ' . (is_writable(dirname($webp_dir)) ? 'Yes' : 'No'));
                }
                throw new Exception(__('Failed to create WebP directory', 'redco-optimizer'));
            }

            // Set proper permissions for newly created directory
            if (file_exists($webp_dir)) {
                @chmod($webp_dir, 0755);
            }
        }

        // Verify directory is writable
        if (!is_writable($webp_dir)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ WebP directory is not writable: ' . $webp_dir);
                error_log('❌ Directory permissions: ' . substr(sprintf('%o', fileperms($webp_dir)), -4));
                error_log('❌ Directory owner: ' . (function_exists('posix_getpwuid') ? posix_getpwuid(fileowner($webp_dir))['name'] : 'Unknown'));
            }

            // Attempt to fix permissions
            if (@chmod($webp_dir, 0755)) {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('✅ Fixed directory permissions for: ' . $webp_dir);
                }
            } else {
                throw new Exception(__('WebP directory is not writable and permissions cannot be fixed', 'redco-optimizer'));
            }
        }

        $image_info = getimagesize($source_path);
        if (!$image_info) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ Invalid image file - getimagesize failed: ' . $source_path);
            }
            throw new Exception(__('Invalid image file', 'redco-optimizer'));
        }

        $mime_type = $image_info['mime'];
        $image_resource = null;

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 Image info - MIME: ' . $mime_type . ', Size: ' . $image_info[0] . 'x' . $image_info[1]);
        }

        // Create image resource based on type
        switch ($mime_type) {
            case 'image/jpeg':
                $image_resource = imagecreatefromjpeg($source_path);
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('🔧 Created JPEG resource: ' . ($image_resource ? 'Success' : 'Failed'));
                }
                break;
            case 'image/png':
                $image_resource = imagecreatefrompng($source_path);
                if ($image_resource) {
                    // Preserve transparency
                    imagealphablending($image_resource, false);
                    imagesavealpha($image_resource, true);
                }
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('🔧 Created PNG resource: ' . ($image_resource ? 'Success' : 'Failed'));
                }
                break;
            case 'image/gif':
                $image_resource = imagecreatefromgif($source_path);
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('🔧 Created GIF resource: ' . ($image_resource ? 'Success' : 'Failed'));
                }
                break;
            default:
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('❌ Unsupported image format: ' . $mime_type);
                }
                throw new Exception(__('Unsupported image format', 'redco-optimizer'));
        }

        if (!$image_resource) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ Failed to create image resource from: ' . $source_path);
            }
            throw new Exception(__('Failed to create image resource', 'redco-optimizer'));
        }

        // Convert to WebP with smart quality settings
        $quality = max(0, min(100, intval($settings['quality'])));

        // Smart lossless decision: only use lossless for PNG images AND if enabled
        $use_lossless = $settings['lossless'] && $mime_type === 'image/png';

        if ($use_lossless) {
            // For lossless WebP (PNG only), use quality 100
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🔧 Converting PNG to lossless WebP (quality 100)');
            }
            $success = imagewebp($image_resource, $webp_path, 100);
        } else {
            // For lossy WebP (JPEG, GIF, or PNG when lossless disabled), use specified quality
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🔧 Converting to lossy WebP (quality ' . $quality . ') - MIME: ' . $mime_type);
            }
            $success = imagewebp($image_resource, $webp_path, $quality);
        }

        // Critical: Check if imagewebp() reported success
        if (!$success) {
            // Clean up image resource on failure
            imagedestroy($image_resource);
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ imagewebp() function returned false for: ' . $webp_path);
                $last_error = error_get_last();
                if ($last_error) {
                    error_log('❌ Last PHP error: ' . $last_error['message'] . ' in ' . $last_error['file'] . ':' . $last_error['line']);
                }
            }
            throw new Exception(__('imagewebp() function failed to convert image', 'redco-optimizer'));
        }

        // Clean up image resource after successful conversion
        imagedestroy($image_resource);

        // CRITICAL FIX: Force file system sync and multiple verification attempts
        if (function_exists('sync')) {
            @sync();
        }

        // Force file system cache clear
        clearstatcache(true, $webp_path);

        // Multiple verification attempts with delays to handle file system latency
        $verification_attempts = 0;
        $max_attempts = 5;
        $file_exists = false;
        $file_valid = false;

        while ($verification_attempts < $max_attempts && !$file_valid) {
            $verification_attempts++;

            if ($verification_attempts > 1) {
                // Progressive delay for file system to catch up
                usleep(50000 * $verification_attempts); // 50ms, 100ms, 150ms, etc.
                clearstatcache(true, $webp_path);
            }

            $file_exists = file_exists($webp_path);

            if ($file_exists) {
                $file_size = filesize($webp_path);
                $file_valid = ($file_size !== false && $file_size > 0);

                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('🔧 File verification attempt ' . $verification_attempts . ': EXISTS, Size: ' . $file_size . ' bytes, Valid: ' . ($file_valid ? 'Yes' : 'No'));
                }
            } else {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('🔧 File verification attempt ' . $verification_attempts . ': NOT FOUND');
                }
            }
        }

        if (!$file_exists) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ CRITICAL: WebP file was not created despite imagewebp() success: ' . $webp_path);
                error_log('❌ Directory exists: ' . (is_dir(dirname($webp_path)) ? 'Yes' : 'No'));
                error_log('❌ Directory writable: ' . (is_writable(dirname($webp_path)) ? 'Yes' : 'No'));
                $dir_contents = @scandir(dirname($webp_path));
                error_log('❌ Directory contents: ' . ($dir_contents ? implode(', ', $dir_contents) : 'Cannot read directory'));
                $last_error = error_get_last();
                if ($last_error) {
                    error_log('❌ Last PHP error: ' . $last_error['message']);
                }
            }
            throw new Exception(__('CRITICAL: WebP file was not created on disk after multiple verification attempts', 'redco-optimizer'));
        }

        if (!$file_valid) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ CRITICAL: WebP file exists but is invalid (0 bytes): ' . $webp_path);
            }
            // Clean up invalid file
            @unlink($webp_path);
            throw new Exception(__('CRITICAL: WebP file was created but is empty or corrupted', 'redco-optimizer'));
        }

        // Verify the WebP file has content
        $webp_size = filesize($webp_path);
        if ($webp_size === false || $webp_size === 0) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ CRITICAL: WebP file is empty or unreadable: ' . $webp_path);
                error_log('❌ File permissions: ' . substr(sprintf('%o', fileperms($webp_path)), -4));
            }
            // Clean up empty file
            if (file_exists($webp_path)) {
                unlink($webp_path);
            }
            throw new Exception(__('CRITICAL: WebP file is empty or corrupted', 'redco-optimizer'));
        }

        // Additional verification: Try to read the WebP file to ensure it's valid
        $webp_info = @getimagesize($webp_path);
        if (!$webp_info || $webp_info['mime'] !== 'image/webp') {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ CRITICAL: Created file is not a valid WebP image: ' . $webp_path);
                error_log('❌ File MIME type: ' . ($webp_info['mime'] ?? 'Unknown'));
            }
            // Clean up invalid file
            if (file_exists($webp_path)) {
                unlink($webp_path);
            }
            throw new Exception(__('CRITICAL: Created file is not a valid WebP image', 'redco-optimizer'));
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            $original_size = filesize($source_path);
            $savings = $original_size - $webp_size;
            $savings_percent = $original_size > 0 ? round(($savings / $original_size) * 100, 1) : 0;
            error_log('✅ WebP conversion successful!');
            error_log('🔧 Original: ' . size_format($original_size) . ' → WebP: ' . size_format($webp_size) . ' (' . $savings_percent . '% savings)');
        }

        return $webp_path;
    }

    /**
     * Check if server can convert to WebP
     */
    private function can_convert_webp() {
        $has_imagewebp = function_exists('imagewebp');
        $has_webp_support = (imagetypes() & IMG_WEBP);

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 WebP Support Check:');
            error_log('  - imagewebp() function: ' . ($has_imagewebp ? 'Available' : 'Missing'));
            error_log('  - IMG_WEBP support: ' . ($has_webp_support ? 'Available' : 'Missing'));

            if (function_exists('gd_info')) {
                $gd_info = gd_info();
                error_log('  - GD Version: ' . ($gd_info['GD Version'] ?? 'Unknown'));
                error_log('  - WebP Support: ' . (isset($gd_info['WebP Support']) && $gd_info['WebP Support'] ? 'Yes' : 'No'));
            }
        }

        return $has_imagewebp && $has_webp_support;
    }

    /**
     * Check if browser supports WebP
     */
    private function browser_supports_webp() {
        if (!isset($_SERVER['HTTP_ACCEPT'])) {
            return false;
        }

        $http_accept = $_SERVER['HTTP_ACCEPT'];
        // CRITICAL FIX: Ensure HTTP_ACCEPT is not null before strpos()
        if (!$http_accept || !is_string($http_accept)) {
            return false;
        }

        return strpos($http_accept, 'image/webp') !== false;
    }

    /**
     * Check if image is convertible
     */
    private function is_convertible_image($file_path) {
        // Ensure file path is valid to prevent wp_is_stream() null parameter warnings
        if (!$file_path || empty($file_path) || !is_string($file_path)) {
            return false;
        }

        $file_info = pathinfo($file_path);
        $extension = strtolower($file_info['extension'] ?? '');

        return in_array($extension, self::SUPPORTED_FORMATS);
    }

    /**
     * Get WebP file path from original
     */
    private function get_webp_path($original_path) {
        // CRITICAL FIX: Ensure original path is valid to prevent pathinfo() null parameter warnings
        if (!$original_path || empty($original_path) || !is_string($original_path)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ get_webp_path called with invalid path: ' . var_export($original_path, true));
            }
            return '';
        }

        $path_info = pathinfo($original_path);

        // CRITICAL FIX: Ensure pathinfo returned valid components
        if (!is_array($path_info) || !isset($path_info['dirname']) || !isset($path_info['filename'])) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ pathinfo failed for: ' . $original_path);
            }
            return '';
        }

        // CRITICAL FIX: Ensure all components are strings
        $dirname = (string) $path_info['dirname'];
        $filename = (string) $path_info['filename'];

        if (empty($dirname) || empty($filename)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ Invalid path components - dirname: ' . $dirname . ', filename: ' . $filename);
            }
            return '';
        }

        return $dirname . '/' . $filename . '.webp';
    }

    /**
     * Get WebP URL from original URL
     */
    private function get_webp_url_from_original($original_url) {
        // CRITICAL FIX: Ensure original_url is not null to prevent str_replace() deprecation warning
        if (!$original_url || !is_string($original_url) || empty($original_url)) {
            return $original_url;
        }

        $upload_dir = wp_upload_dir();
        $base_url = $upload_dir['baseurl'] ?? '';
        $base_dir = $upload_dir['basedir'] ?? '';

        // CRITICAL FIX: Ensure all parameters are strings before str_replace()
        if (!$base_url || !$base_dir) {
            return $original_url;
        }

        $relative_path = str_replace($base_url, '', $original_url);
        $local_path = $base_dir . $relative_path;

        $webp_path = $this->get_webp_path($local_path);
        if (file_exists($webp_path)) {
            $webp_relative = str_replace($base_dir, '', $webp_path);
            return $base_url . $webp_relative;
        }

        return $original_url;
    }

    /**
     * Get module settings
     */
    public function get_settings() {
        $defaults = array(
            'auto_convert_uploads' => true,
            'replace_in_content' => true,
            'quality' => self::DEFAULT_QUALITY,
            'lossless' => self::DEFAULT_LOSSLESS,
            'backup_originals' => true,
            'batch_size' => 10,
            'max_execution_time' => 30
        );

        // Use the standardized option name to match auto-save system
        $settings = get_option('redco_optimizer_smart_webp_conversion', array());

        // Migrate from old option name if it exists and new one is empty
        if (empty($settings)) {
            $old_settings = get_option('redco_webp_settings', array());
            if (!empty($old_settings)) {
                // Migrate old settings to new format
                update_option('redco_optimizer_smart_webp_conversion', $old_settings);
                $settings = $old_settings;

                // Clean up old option
                delete_option('redco_webp_settings');
            }
        }

        return wp_parse_args($settings, $defaults);
    }

    /**
     * Log conversion activity
     */
    private function log_conversion($original_path, $webp_path, $type = 'manual') {
        // CRITICAL FIX: Verify files exist before logging
        if (!file_exists($original_path) || !file_exists($webp_path)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ Cannot log conversion - files missing. Original: ' . (file_exists($original_path) ? 'EXISTS' : 'MISSING') . ', WebP: ' . (file_exists($webp_path) ? 'EXISTS' : 'MISSING'));
            }
            return;
        }

        $original_size = filesize($original_path);
        $webp_size = filesize($webp_path);

        if ($original_size === false || $webp_size === false) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ Cannot log conversion - unable to get file sizes');
            }
            return;
        }

        $log_entry = array(
            'timestamp' => current_time('mysql'),
            'type' => $type,
            'original_path' => $original_path,
            'webp_path' => $webp_path,
            'original_size' => $original_size,
            'webp_size' => $webp_size,
            'savings' => $original_size - $webp_size
        );

        $conversion_log = get_option('redco_webp_conversion_log', array());
        array_unshift($conversion_log, $log_entry);

        // Keep only last 1000 entries
        $conversion_log = array_slice($conversion_log, 0, 1000);

        update_option('redco_webp_conversion_log', $conversion_log);

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('✅ Conversion logged successfully: ' . basename($original_path) . ' → ' . basename($webp_path));
        }
    }

    /**
     * Comprehensive system diagnostics for WebP conversion issues
     */
    public function diagnose_webp_system() {
        $diagnostics = array(
            'php_version' => PHP_VERSION,
            'gd_info' => function_exists('gd_info') ? gd_info() : 'GD not available',
            'imagewebp_available' => function_exists('imagewebp'),
            'webp_support' => (imagetypes() & IMG_WEBP) ? true : false,
            'upload_dir_info' => wp_upload_dir(),
            'file_permissions' => array(),
            'disk_space' => array(),
            'memory_info' => array(
                'memory_limit' => ini_get('memory_limit'),
                'memory_usage' => memory_get_usage(true),
                'memory_peak' => memory_get_peak_usage(true)
            )
        );

        // Test upload directory permissions
        $upload_dir = wp_upload_dir();
        if ($upload_dir && isset($upload_dir['basedir'])) {
            $test_dir = $upload_dir['basedir'] . '/webp-test-' . time();
            $diagnostics['file_permissions']['upload_dir_writable'] = is_writable($upload_dir['basedir']);

            if (wp_mkdir_p($test_dir)) {
                $diagnostics['file_permissions']['can_create_subdirs'] = true;
                $test_file = $test_dir . '/test.txt';
                if (file_put_contents($test_file, 'test') !== false) {
                    $diagnostics['file_permissions']['can_write_files'] = true;
                    unlink($test_file);
                } else {
                    $diagnostics['file_permissions']['can_write_files'] = false;
                }
                rmdir($test_dir);
            } else {
                $diagnostics['file_permissions']['can_create_subdirs'] = false;
            }

            // Check disk space
            if (function_exists('disk_free_space')) {
                $diagnostics['disk_space']['free_bytes'] = disk_free_space($upload_dir['basedir']);
                $diagnostics['disk_space']['free_formatted'] = size_format($diagnostics['disk_space']['free_bytes']);
            }
        }

        return $diagnostics;
    }

    /**
     * Debug and repair conversion data inconsistencies
     */
    public function debug_and_repair_conversion_data() {
        global $wpdb;

        $repair_log = array();

        // Get all images with conversion data
        $images_with_data = $wpdb->get_results("
            SELECT p.ID, pm.meta_value
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'attachment'
            AND pm.meta_key = '_webp_conversion_data'
        ");

        $repair_log[] = 'Found ' . count($images_with_data) . ' images with conversion data';

        foreach ($images_with_data as $image_data) {
            $conversion_data = maybe_unserialize($image_data->meta_value);
            $file_path = get_attached_file($image_data->ID);
            $webp_path = $this->get_webp_path($file_path);

            $repair_log[] = 'Image ID ' . $image_data->ID . ':';
            $repair_log[] = '  - Conversion data: ' . print_r($conversion_data, true);
            $repair_log[] = '  - Original file exists: ' . (file_exists($file_path) ? 'YES' : 'NO');
            $repair_log[] = '  - WebP file exists: ' . (file_exists($webp_path) ? 'YES' : 'NO');

            // Check for inconsistencies
            if (is_array($conversion_data) && isset($conversion_data['converted']) && $conversion_data['converted'] === true) {
                if (!file_exists($webp_path)) {
                    $repair_log[] = '  - INCONSISTENCY: Marked as converted but WebP file missing';
                    // Reset conversion data
                    delete_post_meta($image_data->ID, '_webp_conversion_data');
                    $repair_log[] = '  - FIXED: Removed conversion data for missing WebP file';
                }
            }
        }

        // Check for WebP files without database records
        $all_images = $wpdb->get_results("
            SELECT p.ID
            FROM {$wpdb->posts} p
            WHERE p.post_type = 'attachment'
            AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif')
        ");

        foreach ($all_images as $image) {
            $file_path = get_attached_file($image->ID);
            $webp_path = $this->get_webp_path($file_path);
            $conversion_data = get_post_meta($image->ID, '_webp_conversion_data', true);

            if (file_exists($webp_path) && (!is_array($conversion_data) || !isset($conversion_data['converted']) || $conversion_data['converted'] !== true)) {
                $repair_log[] = 'Image ID ' . $image->ID . ': WebP file exists but no valid conversion data';

                // Create conversion data based on existing files
                $original_size = filesize($file_path);
                $webp_size = filesize($webp_path);

                $new_conversion_data = array(
                    'converted' => true,
                    'conversion_date' => current_time('mysql'),
                    'original_size' => $original_size,
                    'webp_size' => $webp_size,
                    'quality' => 85, // Default quality
                    'lossless' => false
                );

                update_post_meta($image->ID, '_webp_conversion_data', $new_conversion_data);
                $repair_log[] = '  - FIXED: Created conversion data for existing WebP file';
            }
        }

        return $repair_log;
    }

    /**
     * AJAX handler for bulk conversion
     */
    public function ajax_bulk_convert() {
        // Verify nonce and capabilities
        if (!wp_verify_nonce($_POST['nonce'], 'redco_webp_bulk_convert') || !current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'Security check failed'));
            return;
        }

        try {
            $batch_size = intval(isset($_POST['batch_size']) ? $_POST['batch_size'] : 10);
            $offset = intval(isset($_POST['offset']) ? $_POST['offset'] : 0);
            $total_processed = intval(isset($_POST['total_processed']) ? $_POST['total_processed'] : 0);

            // Debug logging
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🔧 WebP Bulk Convert - Batch Size: ' . $batch_size . ', Offset: ' . $offset . ', Total Processed: ' . $total_processed);
            }

            // Get images to convert
            $images = $this->get_unconverted_images($batch_size, $offset);

            // Debug logging for image detection
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🔧 WebP Images Found: ' . count($images));
                if (!empty($images)) {
                    $image_ids = array_map(function($img) { return $img->ID; }, $images);
                    error_log('🔧 WebP Image IDs: ' . implode(', ', $image_ids));
                    foreach ($images as $img) {
                        $file_path = get_attached_file($img->ID);
                        $conversion_data = get_post_meta($img->ID, '_webp_conversion_data', true);
                        // CRITICAL FIX: Ensure file_path is not null before basename()
                        $file_name = $file_path ? basename($file_path) : 'Unknown file';
                        error_log('🔧 Image ID ' . $img->ID . ' - File: ' . $file_name . ', Conversion Data: ' . ($conversion_data ? 'EXISTS' : 'NONE'));
                    }
                } else {
                    error_log('🔧 No unconverted images found - checking why...');
                    // Check total images vs converted images
                    global $wpdb;
                    $total_images = $wpdb->get_var("
                        SELECT COUNT(*)
                        FROM {$wpdb->posts}
                        WHERE post_type = 'attachment'
                        AND post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif')
                    ");
                    $converted_images = $wpdb->get_var("
                        SELECT COUNT(*)
                        FROM {$wpdb->posts} p
                        INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
                        WHERE p.post_type = 'attachment'
                        AND pm.meta_key = '_webp_conversion_data'
                        AND pm.meta_value LIKE '%converted\";b:1%'
                    ");
                    error_log('🔧 Total images: ' . $total_images . ', Converted images: ' . $converted_images);
                }
            }

            $settings = $this->get_settings();

            $results = array(
                'success' => true,
                'processed' => 0,
                'errors' => array(),
                'conversions' => array(),
                'total_processed' => $total_processed,
                'has_more' => false
            );

            foreach ($images as $image) {
                try {
                    $file_path = get_attached_file($image->ID);
                    if (!$file_path || !file_exists($file_path)) {
                        if (defined('WP_DEBUG') && WP_DEBUG) {
                            error_log('⚠️ Skipping image ID ' . $image->ID . ' - file not found: ' . $file_path);
                        }
                        continue;
                    }

                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        error_log('🔧 Processing image ID ' . $image->ID . ': ' . $file_path);
                        error_log('🔧 File exists: ' . (file_exists($file_path) ? 'Yes' : 'No'));
                        error_log('🔧 File size: ' . filesize($file_path) . ' bytes');
                        error_log('🔧 Settings: ' . print_r($settings, true));
                    }

                    // Attempt WebP conversion - this will throw exception if it fails
                    $webp_path = $this->convert_to_webp($file_path, $settings);

                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        error_log('🔧 convert_to_webp() returned: ' . $webp_path);
                        error_log('🔧 WebP file exists after conversion: ' . (file_exists($webp_path) ? 'Yes' : 'No'));
                        if (file_exists($webp_path)) {
                            error_log('🔧 WebP file size: ' . filesize($webp_path) . ' bytes');
                        }
                    }

                    // CRITICAL: Triple-verify file creation before database updates
                    clearstatcache(); // Clear file cache
                    $file_verification_passed = false;

                    if ($webp_path && file_exists($webp_path) && filesize($webp_path) > 0) {
                        // Additional verification: Check if file is readable and valid
                        $webp_info = @getimagesize($webp_path);
                        if ($webp_info && $webp_info['mime'] === 'image/webp') {
                            $file_verification_passed = true;
                            if (defined('WP_DEBUG') && WP_DEBUG) {
                                error_log('✅ VERIFIED: WebP file created and valid: ' . $webp_path);
                            }
                        } else {
                            if (defined('WP_DEBUG') && WP_DEBUG) {
                                error_log('❌ CRITICAL: WebP file exists but is invalid: ' . $webp_path);
                                error_log('❌ File MIME: ' . ($webp_info['mime'] ?? 'Unknown'));
                            }
                        }
                    } else {
                        if (defined('WP_DEBUG') && WP_DEBUG) {
                            error_log('❌ CRITICAL: WebP file verification failed for: ' . $webp_path);
                            error_log('❌ File exists: ' . (file_exists($webp_path) ? 'Yes' : 'No'));
                            error_log('❌ File size: ' . (file_exists($webp_path) ? filesize($webp_path) : 'N/A'));
                        }
                    }

                    // Only proceed with database updates if file verification passed
                    if ($file_verification_passed) {
                        // Update metadata
                        $metadata = wp_get_attachment_metadata($image->ID);
                        if (!$metadata) $metadata = array();

                        $upload_dir = wp_upload_dir();
                        $metadata['webp_versions']['full'] = str_replace($upload_dir['basedir'], '', $webp_path);

                        wp_update_attachment_metadata($image->ID, $metadata);

                        // Get actual file sizes
                        $original_size = filesize($file_path);
                        $webp_size = filesize($webp_path);

                        // Store conversion data
                        update_post_meta($image->ID, '_webp_conversion_data', array(
                            'converted' => true,
                            'conversion_date' => current_time('mysql'),
                            'original_size' => $original_size,
                            'webp_size' => $webp_size,
                            'quality' => $settings['quality'],
                            'lossless' => $settings['lossless']
                        ));

                        // Log conversion
                        $this->log_conversion($file_path, $webp_path, 'bulk');

                        // Add to results
                        $results['conversions'][] = array(
                            'id' => $image->ID,
                            'title' => get_the_title($image->ID),
                            'original_size' => $original_size,
                            'webp_size' => $webp_size,
                            'savings' => $original_size - $webp_size
                        );

                        $results['processed']++;

                        if (defined('WP_DEBUG') && WP_DEBUG) {
                            error_log('✅ Successfully processed image ID ' . $image->ID);
                        }
                    } else {
                        // CRITICAL: File verification failed - provide detailed error
                        $error_details = array();
                        if (!$webp_path) {
                            $error_details[] = 'WebP path is empty';
                        } elseif (!file_exists($webp_path)) {
                            $error_details[] = 'WebP file was not created';
                        } elseif (filesize($webp_path) === 0) {
                            $error_details[] = 'WebP file is empty (0 bytes)';
                        } else {
                            $webp_info = @getimagesize($webp_path);
                            if (!$webp_info) {
                                $error_details[] = 'WebP file is corrupted or unreadable';
                            } elseif ($webp_info['mime'] !== 'image/webp') {
                                $error_details[] = 'File is not a valid WebP (MIME: ' . $webp_info['mime'] . ')';
                            }
                        }

                        $error_message = 'WebP file verification failed: ' . implode(', ', $error_details);
                        throw new Exception($error_message);
                    }
                } catch (Exception $e) {
                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        error_log('❌ Error processing image ID ' . $image->ID . ': ' . $e->getMessage());
                    }
                    $results['errors'][] = array(
                        'id' => $image->ID,
                        'title' => get_the_title($image->ID),
                        'error' => $e->getMessage()
                    );
                }
            }

            $results['total_processed'] = $total_processed + $results['processed'];

            // Check if there are more images to process
            $remaining_images = $this->get_unconverted_images(1, $offset + $batch_size);
            $results['has_more'] = !empty($remaining_images);

            wp_send_json($results);

        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => 'Conversion failed: ' . $e->getMessage()
            ));
        } catch (Error $e) {
            wp_send_json_error(array(
                'message' => 'Fatal error during conversion: ' . $e->getMessage()
            ));
        }
    }

    /**
     * AJAX handler for getting statistics
     */
    public function ajax_get_stats() {
        if (!wp_verify_nonce($_POST['nonce'], 'redco_webp_stats') || !current_user_can('manage_options')) {
            wp_die(__('Security check failed', 'redco-optimizer'));
        }

        wp_send_json($this->get_stats());
    }

    /**
     * AJAX handler for rollback conversion
     */
    public function ajax_rollback_conversion() {
        if (!wp_verify_nonce($_POST['nonce'], 'redco_webp_rollback') || !current_user_can('manage_options')) {
            wp_die(__('Security check failed', 'redco-optimizer'));
        }

        $attachment_id = intval($_POST['attachment_id']);
        $result = $this->rollback_conversion($attachment_id);

        wp_send_json($result);
    }

    /**
     * AJAX handler for testing conversion capability
     */
    public function ajax_test_conversion() {
        if (!wp_verify_nonce($_POST['nonce'], 'redco_webp_test') || !current_user_can('manage_options')) {
            wp_die(__('Security check failed', 'redco-optimizer'));
        }

        $result = array(
            'success' => false,
            'message' => '',
            'capabilities' => array()
        );

        // Test WebP support
        $result['capabilities']['webp_support'] = $this->can_convert_webp();
        $result['capabilities']['gd_version'] = function_exists('gd_info') ? gd_info()['GD Version'] : 'Not available';
        $result['capabilities']['supported_formats'] = array();

        if (function_exists('imagetypes')) {
            $types = imagetypes();
            $result['capabilities']['supported_formats']['jpeg'] = (bool)($types & IMG_JPG);
            $result['capabilities']['supported_formats']['png'] = (bool)($types & IMG_PNG);
            $result['capabilities']['supported_formats']['gif'] = (bool)($types & IMG_GIF);
            $result['capabilities']['supported_formats']['webp'] = (bool)($types & IMG_WEBP);
        }

        if ($result['capabilities']['webp_support']) {
            $result['success'] = true;
            $result['message'] = __('Server supports WebP conversion', 'redco-optimizer');
        } else {
            $result['message'] = __('Server does not support WebP conversion. Please contact your hosting provider.', 'redco-optimizer');
        }

        wp_send_json($result);
    }

    /**
     * Get unconverted images
     */
    public function get_unconverted_images($limit = 10, $offset = 0) {
        global $wpdb;

        // CRITICAL DEBUG: First, let's examine what's actually in the database
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 DEBUG: Investigating actual database content for WebP conversion data...');

            // Get all conversion data to see the actual serialization format
            $debug_query = "
                SELECT p.ID, pm.meta_value
                FROM {$wpdb->posts} p
                INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
                WHERE p.post_type = 'attachment'
                AND pm.meta_key = '_webp_conversion_data'
                LIMIT 10
            ";

            $debug_results = $wpdb->get_results($debug_query);
            error_log('🔧 DEBUG: Found ' . count($debug_results) . ' records with _webp_conversion_data');

            foreach ($debug_results as $debug_row) {
                error_log('🔧 DEBUG: ID ' . $debug_row->ID . ' meta_value: ' . $debug_row->meta_value);
                $unserialized = maybe_unserialize($debug_row->meta_value);
                if (is_array($unserialized)) {
                    error_log('🔧 DEBUG: ID ' . $debug_row->ID . ' unserialized: ' . print_r($unserialized, true));
                    error_log('🔧 DEBUG: ID ' . $debug_row->ID . ' converted value: ' . ($unserialized['converted'] ?? 'NOT SET'));
                }
            }
        }

        // CRITICAL FIX: Use proper method to check for converted images
        // Instead of relying on LIKE patterns, let's get all images and filter programmatically
        $all_images_query = "
            SELECT p.ID
            FROM {$wpdb->posts} p
            WHERE p.post_type = 'attachment'
            AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif')
            ORDER BY p.post_date DESC
        ";

        $all_images = $wpdb->get_results($all_images_query);

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 DEBUG: Found ' . count($all_images) . ' total images in database');
        }

        // Filter out already converted images programmatically
        $unconverted_images = array();
        $converted_count = 0;

        foreach ($all_images as $image) {
            $conversion_data = get_post_meta($image->ID, '_webp_conversion_data', true);

            if (defined('WP_DEBUG') && WP_DEBUG && count($unconverted_images) < 5) {
                error_log('🔧 DEBUG: Image ID ' . $image->ID . ' conversion_data: ' . print_r($conversion_data, true));
                error_log('🔧 DEBUG: Image ID ' . $image->ID . ' is_array: ' . (is_array($conversion_data) ? 'YES' : 'NO'));
                if (is_array($conversion_data)) {
                    error_log('🔧 DEBUG: Image ID ' . $image->ID . ' converted flag: ' . (isset($conversion_data['converted']) ? ($conversion_data['converted'] ? 'TRUE' : 'FALSE') : 'NOT SET'));
                }
            }

            // Check if image is already converted
            $is_converted = false;
            if (is_array($conversion_data) && isset($conversion_data['converted']) && $conversion_data['converted'] === true) {
                $is_converted = true;
                $converted_count++;
            }

            if (!$is_converted) {
                // Additional validation: Check if files actually exist
                $file_path = get_attached_file($image->ID);
                if ($file_path && file_exists($file_path) && is_readable($file_path)) {
                    // Double-check: Verify WebP doesn't already exist
                    $webp_path = $this->get_webp_path($file_path);
                    if (!file_exists($webp_path)) {
                        $unconverted_images[] = $image;
                    } else {
                        if (defined('WP_DEBUG') && WP_DEBUG) {
                            error_log('🔧 DEBUG: Image ID ' . $image->ID . ' has WebP file but no database record: ' . $webp_path);
                        }
                    }
                }
            }
        }

        // Apply offset and limit
        $total_unconverted = count($unconverted_images);
        $unconverted_images = array_slice($unconverted_images, $offset, $limit);

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 DEBUG: Total images: ' . count($all_images) . ', Converted: ' . $converted_count . ', Unconverted: ' . $total_unconverted . ', Returning: ' . count($unconverted_images));
        }

        return $unconverted_images;
    }

    /**
     * Get module statistics
     */
    public function get_stats() {
        global $wpdb;

        // Total images
        $total_images = $wpdb->get_var("
            SELECT COUNT(*)
            FROM {$wpdb->posts}
            WHERE post_type = 'attachment'
            AND post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif')
        ");

        // CRITICAL FIX: Use programmatic approach instead of LIKE patterns
        // Get all images and check conversion status programmatically
        $all_images = $wpdb->get_results("
            SELECT p.ID
            FROM {$wpdb->posts} p
            WHERE p.post_type = 'attachment'
            AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif')
        ");

        $converted_images = 0;
        $total_original_size = 0;
        $total_webp_size = 0;
        $conversion_details = array();

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 STATS DEBUG: Checking ' . count($all_images) . ' total images for conversion status');
        }

        foreach ($all_images as $image) {
            $conversion_data = get_post_meta($image->ID, '_webp_conversion_data', true);

            // Check if image is converted
            if (is_array($conversion_data) && isset($conversion_data['converted']) && $conversion_data['converted'] === true) {
                $converted_images++;

                // Add to savings calculation
                $original_size = intval($conversion_data['original_size'] ?? 0);
                $webp_size = intval($conversion_data['webp_size'] ?? 0);

                $total_original_size += $original_size;
                $total_webp_size += $webp_size;

                $conversion_details[] = array(
                    'id' => $image->ID,
                    'original_size' => $original_size,
                    'webp_size' => $webp_size,
                    'conversion_date' => $conversion_data['conversion_date'] ?? 'Unknown'
                );

                if (defined('WP_DEBUG') && WP_DEBUG && count($conversion_details) <= 5) {
                    error_log('🔧 STATS DEBUG: Image ID ' . $image->ID . ' is converted - Original: ' . $original_size . ', WebP: ' . $webp_size);
                }
            }
        }

        $total_savings = $total_original_size - $total_webp_size;
        $savings_percentage = $total_original_size > 0 ? round(($total_savings / $total_original_size) * 100, 1) : 0;

        // Recent conversions
        $conversion_log = get_option('redco_webp_conversion_log', array());
        $recent_conversions = array_slice($conversion_log, 0, 10);

        $stats = array(
            'total_images' => intval($total_images),
            'converted_images' => $converted_images,
            'unconverted_images' => intval($total_images) - $converted_images,
            'conversion_percentage' => $total_images > 0 ? round(($converted_images / intval($total_images)) * 100, 1) : 0,
            'total_original_size' => $total_original_size,
            'total_webp_size' => $total_webp_size,
            'total_savings' => $total_savings,
            'savings_percentage' => $savings_percentage,
            'recent_conversions' => $recent_conversions,
            'server_support' => $this->can_convert_webp(),
            'browser_support' => $this->browser_supports_webp()
        );

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 STATS DEBUG: Final stats - Total: ' . $stats['total_images'] . ', Converted: ' . $stats['converted_images'] . ', Unconverted: ' . $stats['unconverted_images']);
        }

        return $stats;
    }

    /**
     * Get optimization tips based on current state
     */
    public function get_optimization_tips() {
        $tips = array();
        $stats = $this->get_stats();
        $settings = $this->get_settings();

        // Server support tip
        if (!$stats['server_support']) {
            $tips[] = array(
                'type' => 'warning',
                'title' => __('Server Compatibility', 'redco-optimizer'),
                'message' => __('Your server does not support WebP conversion. Contact your hosting provider to enable GD library with WebP support.', 'redco-optimizer')
            );
        }

        // Conversion progress tip
        if ($stats['server_support'] && $stats['unconverted_images'] > 0) {
            $tips[] = array(
                'type' => 'info',
                'title' => __('Pending Conversions', 'redco-optimizer'),
                'message' => sprintf(__('You have %d images that can be converted to WebP format. Use the bulk conversion tool to optimize them.', 'redco-optimizer'), $stats['unconverted_images'])
            );
        }

        // Quality optimization tip
        if ($settings['quality'] > 95) {
            $tips[] = array(
                'type' => 'info',
                'title' => __('Quality Settings', 'redco-optimizer'),
                'message' => __('Consider reducing quality to 85-90% for better compression while maintaining excellent visual quality.', 'redco-optimizer')
            );
        }

        // Backup recommendation
        if (!$settings['backup_originals']) {
            $tips[] = array(
                'type' => 'warning',
                'title' => __('Backup Safety', 'redco-optimizer'),
                'message' => __('Enable backup of original images for safety. This allows you to rollback conversions if needed.', 'redco-optimizer')
            );
        }

        // Success tip when everything is optimized
        if ($stats['server_support'] && $stats['unconverted_images'] === 0 && $stats['converted_images'] > 0) {
            $tips[] = array(
                'type' => 'success',
                'title' => __('Fully Optimized', 'redco-optimizer'),
                'message' => sprintf(__('Excellent! All %d images are converted to WebP format, saving %s of storage space.', 'redco-optimizer'), $stats['converted_images'], size_format($stats['total_savings']))
            );
        }

        return $tips;
    }

    /**
     * Rollback conversion for specific attachment
     */
    private function rollback_conversion($attachment_id) {
        $conversion_data = get_post_meta($attachment_id, '_webp_conversion_data', true);

        if (!$conversion_data || !$conversion_data['converted']) {
            return array(
                'success' => false,
                'message' => __('Image was not converted to WebP', 'redco-optimizer')
            );
        }

        // Remove WebP files
        $metadata = wp_get_attachment_metadata($attachment_id);
        if (isset($metadata['webp_versions'])) {
            $upload_dir = wp_upload_dir();

            foreach ($metadata['webp_versions'] as $webp_relative_path) {
                $webp_full_path = $upload_dir['basedir'] . $webp_relative_path;
                if (file_exists($webp_full_path)) {
                    unlink($webp_full_path);
                }
            }

            // Remove WebP data from metadata
            unset($metadata['webp_versions']);
            wp_update_attachment_metadata($attachment_id, $metadata);
        }

        // Remove conversion data
        delete_post_meta($attachment_id, '_webp_conversion_data');

        return array(
            'success' => true,
            'message' => __('WebP conversion rolled back successfully', 'redco-optimizer')
        );
    }

    // AJAX debug handler removed - now handled globally to work regardless of module status


}





// Enqueue scripts regardless of module status (for admin interface)
function redco_webp_enqueue_admin_scripts($hook) {
    // Only load on Redco Optimizer pages - with comprehensive null safety
    if (!$hook || !is_string($hook) || strpos($hook, 'redco-optimizer') === false) {
        return;
    }

    // Load on smart-webp-conversion tab or if no tab specified (default tab)
    $current_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'dashboard';

    // Debug logging for script loading
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('🔧 WebP Script Loading Check - Hook: ' . $hook . ', Tab: ' . $current_tab . ', Module Enabled: ' . (redco_is_module_enabled('smart-webp-conversion') ? 'Yes' : 'No'));
    }

    // Force script loading for debugging - load on all Redco Optimizer pages
    if (true) {
        // Enqueue CSS
        wp_enqueue_style(
            'redco-webp-admin',
            REDCO_OPTIMIZER_PLUGIN_URL . 'modules/smart-webp-conversion/assets/css/admin.css',
            array(),
            REDCO_OPTIMIZER_VERSION
        );

        // Enqueue JavaScript with cache-busting (with file existence check)
        $js_file_path = REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/smart-webp-conversion/assets/js/admin.js';
        $js_version = REDCO_OPTIMIZER_VERSION;
        if (file_exists($js_file_path)) {
            $js_version .= '.' . filemtime($js_file_path);
        }

        wp_enqueue_script(
            'redco-webp-admin',
            REDCO_OPTIMIZER_PLUGIN_URL . 'modules/smart-webp-conversion/assets/js/admin.js',
            array('jquery'),
            $js_version,
            true
        );

        // Localize script for AJAX
        wp_localize_script('redco-webp-admin', 'redcoWebP', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('redco_optimizer_nonce'),
            'current_tab' => $current_tab,
            'module_enabled' => redco_is_module_enabled('smart-webp-conversion'),
            'nonces' => array(
                'bulk_convert' => wp_create_nonce('redco_webp_bulk_convert'),
                'stats' => wp_create_nonce('redco_webp_stats'),
                'rollback' => wp_create_nonce('redco_webp_rollback'),
                'test' => wp_create_nonce('redco_webp_test')
            ),
            'strings' => array(
                'converting' => __('Converting...', 'redco-optimizer'),
                'converted' => __('Converted', 'redco-optimizer'),
                'error' => __('Error', 'redco-optimizer'),
                'complete' => __('Conversion Complete', 'redco-optimizer'),
                'confirm_rollback' => __('Are you sure you want to rollback this conversion?', 'redco-optimizer')
            )
        ));

        // Debug logging for successful script loading
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('✅ WebP Scripts and styles enqueued successfully for tab: ' . $current_tab);
        }
    }
}

// Global AJAX handler for debugging (works regardless of module status)
function redco_webp_ajax_debug_images() {
    // Accept multiple nonce types for compatibility
    $nonce_valid = false;
    if (isset($_POST['nonce'])) {
        $nonce_valid = wp_verify_nonce($_POST['nonce'], 'redco_webp_stats') ||
                      wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce');
    }

    if (!$nonce_valid) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    global $wpdb;

    try {
        // Get all attachment posts
        $all_attachments = $wpdb->get_results("
            SELECT ID, post_title, post_mime_type
            FROM {$wpdb->posts}
            WHERE post_type = 'attachment'
            ORDER BY post_date DESC
        ");

        // Get image attachments specifically
        $image_attachments = $wpdb->get_results("
            SELECT ID, post_title, post_mime_type
            FROM {$wpdb->posts}
            WHERE post_type = 'attachment'
            AND post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif')
            ORDER BY post_date DESC
        ");

        $debug_data = array(
            'total_attachments' => count($all_attachments),
            'total_images' => count($image_attachments),
            'image_ids' => array_map(function($img) { return $img->ID; }, $image_attachments),
            'mime_types' => array_map(function($img) { return $img->post_mime_type; }, $image_attachments),
            'all_attachment_types' => array_unique(array_map(function($att) { return $att->post_mime_type; }, $all_attachments))
        );

        wp_send_json_success($debug_data);
    } catch (Exception $e) {
        wp_send_json_error('Database error: ' . $e->getMessage());
    }
}

// Global WebP diagnostics AJAX handler
function redco_webp_ajax_system_diagnostics() {
    // Verify nonce and capabilities
    if (!wp_verify_nonce($_POST['nonce'], 'redco_webp_stats') || !current_user_can('manage_options')) {
        wp_send_json_error('Security check failed');
        return;
    }

    // Get module instance
    global $redco_webp_instance;
    if (!$redco_webp_instance && class_exists('Redco_Smart_WebP_Conversion')) {
        $redco_webp_instance = new Redco_Smart_WebP_Conversion();
    }

    if ($redco_webp_instance && method_exists($redco_webp_instance, 'diagnose_webp_system')) {
        $diagnostics = $redco_webp_instance->diagnose_webp_system();
        wp_send_json_success($diagnostics);
    } else {
        wp_send_json_error('WebP diagnostics not available');
    }
}

// Global WebP repair AJAX handler
function redco_webp_ajax_repair_data() {
    // Verify nonce and capabilities
    if (!wp_verify_nonce($_POST['nonce'], 'redco_webp_stats') || !current_user_can('manage_options')) {
        wp_send_json_error('Security check failed');
        return;
    }

    // Get module instance
    global $redco_webp_instance;
    if (!$redco_webp_instance && class_exists('Redco_Smart_WebP_Conversion')) {
        $redco_webp_instance = new Redco_Smart_WebP_Conversion();
    }

    if ($redco_webp_instance && method_exists($redco_webp_instance, 'debug_and_repair_conversion_data')) {
        $repair_log = $redco_webp_instance->debug_and_repair_conversion_data();
        wp_send_json_success(array(
            'message' => 'Repair completed',
            'repair_log' => $repair_log
        ));
    } else {
        wp_send_json_error('WebP repair function not available');
    }
}

// Global WebP debug analysis AJAX handler
function redco_webp_ajax_debug_analysis() {
    // Verify nonce and capabilities
    if (!wp_verify_nonce($_POST['nonce'], 'redco_webp_stats') || !current_user_can('manage_options')) {
        wp_send_json_error('Security check failed');
        return;
    }

    global $wpdb;

    $debug_data = array();

    // Test 1: All images in database
    $all_images = $wpdb->get_results("
        SELECT p.ID, p.post_title, p.post_mime_type
        FROM {$wpdb->posts} p
        WHERE p.post_type = 'attachment'
        AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif')
        ORDER BY p.post_date DESC
    ");

    $debug_data['total_images'] = count($all_images);
    $debug_data['image_list'] = array();

    foreach ($all_images as $img) {
        $debug_data['image_list'][] = array(
            'id' => $img->ID,
            'title' => $img->post_title,
            'type' => $img->post_mime_type
        );
    }

    // Test 2: Conversion data in database
    $conversion_data = $wpdb->get_results("
        SELECT p.ID, pm.meta_value
        FROM {$wpdb->posts} p
        INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
        WHERE p.post_type = 'attachment'
        AND pm.meta_key = '_webp_conversion_data'
    ");

    $debug_data['conversion_records'] = count($conversion_data);
    $debug_data['conversion_details'] = array();

    foreach ($conversion_data as $data) {
        $unserialized = maybe_unserialize($data->meta_value);
        $debug_data['conversion_details'][] = array(
            'id' => $data->ID,
            'raw_meta' => $data->meta_value,
            'unserialized' => $unserialized,
            'is_array' => is_array($unserialized),
            'converted_flag' => is_array($unserialized) && isset($unserialized['converted']) ? $unserialized['converted'] : 'NOT SET'
        );
    }

    // Test 3: File existence check
    $debug_data['file_analysis'] = array();

    if (class_exists('Redco_Smart_WebP_Conversion')) {
        $webp_instance = new Redco_Smart_WebP_Conversion();

        foreach ($all_images as $img) {
            $file_path = get_attached_file($img->ID);

            // Use reflection to access private method
            $webp_path = '';
            try {
                $reflection = new ReflectionClass($webp_instance);
                $method = $reflection->getMethod('get_webp_path');
                $method->setAccessible(true);
                $webp_path = $method->invoke($webp_instance, $file_path);
            } catch (Exception $e) {
                $webp_path = 'Error: ' . $e->getMessage();
            }

            $debug_data['file_analysis'][] = array(
                'id' => $img->ID,
                'original_file' => $file_path ? basename($file_path) : 'N/A',
                'original_exists' => file_exists($file_path),
                'webp_file' => $webp_path ? basename($webp_path) : 'N/A',
                'webp_exists' => file_exists($webp_path),
                'webp_size' => file_exists($webp_path) ? filesize($webp_path) : 0
            );
        }
    }

    // Test 4: Programmatic approach test
    $converted_count = 0;
    $unconverted_count = 0;
    $debug_data['programmatic_analysis'] = array();

    foreach ($all_images as $img) {
        $conversion_data = get_post_meta($img->ID, '_webp_conversion_data', true);
        $is_converted = is_array($conversion_data) && isset($conversion_data['converted']) && $conversion_data['converted'] === true;

        if ($is_converted) {
            $converted_count++;
        } else {
            $unconverted_count++;
        }

        $debug_data['programmatic_analysis'][] = array(
            'id' => $img->ID,
            'is_converted' => $is_converted,
            'conversion_data' => $conversion_data
        );
    }

    $debug_data['programmatic_summary'] = array(
        'total' => count($all_images),
        'converted' => $converted_count,
        'unconverted' => $unconverted_count
    );

    // Test 5: Current stats method
    if (class_exists('Redco_Smart_WebP_Conversion')) {
        $webp_instance = new Redco_Smart_WebP_Conversion();
        if (method_exists($webp_instance, 'get_stats')) {
            $debug_data['current_stats'] = $webp_instance->get_stats();
        }
    }

    wp_send_json_success($debug_data);
}

// Global WebP creation test AJAX handler
function redco_webp_ajax_test_creation() {
    // Verify nonce and capabilities
    if (!wp_verify_nonce($_POST['nonce'], 'redco_webp_stats') || !current_user_can('manage_options')) {
        wp_send_json_error('Security check failed');
        return;
    }

    global $wpdb;

    try {
        $result = array(
            'test_image_found' => false,
            'test_image_path' => '',
            'webp_support' => false,
            'directory_writable' => false,
            'conversion_attempted' => false,
            'imagewebp_success' => false,
            'file_created' => false,
            'file_size' => 0,
            'valid_webp' => false,
            'error_details' => '',
            'php_errors' => array()
        );

        // Find a test image from the media library
        $test_image = $wpdb->get_row("
            SELECT p.ID
            FROM {$wpdb->posts} p
            WHERE p.post_type = 'attachment'
            AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif')
            ORDER BY p.post_date DESC
            LIMIT 1
        ");

        if (!$test_image) {
            wp_send_json_error('No test images found in media library');
            return;
        }

        $file_path = get_attached_file($test_image->ID);
        if (!$file_path || !file_exists($file_path)) {
            wp_send_json_error('Test image file not found: ' . $file_path);
            return;
        }

        $result['test_image_found'] = true;
        $result['test_image_path'] = $file_path;

        // Check WebP support
        $result['webp_support'] = function_exists('imagewebp') && (imagetypes() & IMG_WEBP);

        if (!$result['webp_support']) {
            wp_send_json_success($result);
            return;
        }

        // Get WebP path
        $path_info = pathinfo($file_path);
        $webp_path = $path_info['dirname'] . '/test_webp_creation.webp';

        // Check directory writable
        $result['directory_writable'] = is_writable(dirname($webp_path));

        if (!$result['directory_writable']) {
            wp_send_json_success($result);
            return;
        }

        // Clear any existing test file
        if (file_exists($webp_path)) {
            unlink($webp_path);
        }

        // Attempt conversion
        $result['conversion_attempted'] = true;

        // Get image info
        $image_info = getimagesize($file_path);
        if (!$image_info) {
            $result['error_details'] = 'getimagesize() failed for test image';
            wp_send_json_success($result);
            return;
        }

        // Create image resource
        $image_resource = null;
        switch ($image_info['mime']) {
            case 'image/jpeg':
                $image_resource = imagecreatefromjpeg($file_path);
                break;
            case 'image/png':
                $image_resource = imagecreatefrompng($file_path);
                break;
            case 'image/gif':
                $image_resource = imagecreatefromgif($file_path);
                break;
        }

        if (!$image_resource) {
            $result['error_details'] = 'Failed to create image resource from test image';
            wp_send_json_success($result);
            return;
        }

        // Attempt WebP conversion
        $result['imagewebp_success'] = imagewebp($image_resource, $webp_path, 85);
        imagedestroy($image_resource);

        // Check if file was created
        clearstatcache();
        $result['file_created'] = file_exists($webp_path);

        if ($result['file_created']) {
            $result['file_size'] = filesize($webp_path);

            // Verify it's a valid WebP
            $webp_info = @getimagesize($webp_path);
            $result['valid_webp'] = $webp_info && $webp_info['mime'] === 'image/webp';

            // Clean up test file
            unlink($webp_path);
        }

        wp_send_json_success($result);

    } catch (Exception $e) {
        wp_send_json_error('WebP creation test failed: ' . $e->getMessage());
    }
}

// Global WebP reset specific images AJAX handler
function redco_webp_ajax_reset_specific_images() {
    // Verify nonce and capabilities
    if (!wp_verify_nonce($_POST['nonce'], 'redco_webp_stats') || !current_user_can('manage_options')) {
        wp_send_json_error('Security check failed');
        return;
    }

    if (!isset($_POST['filenames']) || !is_array($_POST['filenames'])) {
        wp_send_json_error('No filenames provided');
        return;
    }

    global $wpdb;

    try {
        $filenames = array_map('sanitize_text_field', $_POST['filenames']);
        $images_found = 0;
        $records_cleared = 0;
        $files_deleted = 0;
        $processed_images = array();

        foreach ($filenames as $filename) {
            // Find attachment by filename
            $attachment = $wpdb->get_row($wpdb->prepare("
                SELECT p.ID, p.post_title
                FROM {$wpdb->posts} p
                INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
                WHERE p.post_type = 'attachment'
                AND pm.meta_key = '_wp_attached_file'
                AND pm.meta_value LIKE %s
            ", '%' . $filename));

            if ($attachment) {
                $images_found++;
                $file_path = get_attached_file($attachment->ID);

                if ($file_path) {
                    // Get WebP path
                    $path_info = pathinfo($file_path);
                    $webp_path = $path_info['dirname'] . '/' . $path_info['filename'] . '.webp';

                    // Delete WebP file if it exists
                    if (file_exists($webp_path)) {
                        if (unlink($webp_path)) {
                            $files_deleted++;
                        }
                    }

                    // Clear metadata
                    $metadata = wp_get_attachment_metadata($attachment->ID);
                    if (isset($metadata['webp_versions'])) {
                        unset($metadata['webp_versions']);
                        wp_update_attachment_metadata($attachment->ID, $metadata);
                    }

                    // Delete conversion data
                    delete_post_meta($attachment->ID, '_webp_conversion_data');
                    $records_cleared++;

                    $processed_images[] = $filename . ' (ID: ' . $attachment->ID . ')';
                }
            }
        }

        wp_send_json_success(array(
            'images_found' => $images_found,
            'records_cleared' => $records_cleared,
            'files_deleted' => $files_deleted,
            'processed_images' => $processed_images
        ));

    } catch (Exception $e) {
        wp_send_json_error('Specific reset failed: ' . $e->getMessage());
    }
}

// Global WebP reset conversions AJAX handler
function redco_webp_ajax_reset_conversions() {
    // Verify nonce and capabilities
    if (!wp_verify_nonce($_POST['nonce'], 'redco_webp_stats') || !current_user_can('manage_options')) {
        wp_send_json_error('Security check failed');
        return;
    }

    global $wpdb;

    try {
        // Get all conversion records before deletion
        $conversion_records = $wpdb->get_results("
            SELECT p.ID
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'attachment'
            AND pm.meta_key = '_webp_conversion_data'
        ");

        $records_cleared = 0;
        $files_deleted = 0;

        // Delete WebP files and clear database records
        foreach ($conversion_records as $record) {
            $file_path = get_attached_file($record->ID);
            if ($file_path) {
                // Get WebP path
                $path_info = pathinfo($file_path);
                $webp_path = $path_info['dirname'] . '/' . $path_info['filename'] . '.webp';

                // Delete WebP file if it exists
                if (file_exists($webp_path)) {
                    if (unlink($webp_path)) {
                        $files_deleted++;
                    }
                }

                // Clear metadata
                $metadata = wp_get_attachment_metadata($record->ID);
                if (isset($metadata['webp_versions'])) {
                    unset($metadata['webp_versions']);
                    wp_update_attachment_metadata($record->ID, $metadata);
                }

                // Delete conversion data
                delete_post_meta($record->ID, '_webp_conversion_data');
                $records_cleared++;
            }
        }

        // Clear conversion log
        delete_option('redco_webp_conversion_log');
        $log_cleared = true;

        wp_send_json_success(array(
            'records_cleared' => $records_cleared,
            'files_deleted' => $files_deleted,
            'log_cleared' => $log_cleared
        ));

    } catch (Exception $e) {
        wp_send_json_error('Reset failed: ' . $e->getMessage());
    }
}

// Global WebP file verification AJAX handler
function redco_webp_ajax_verify_files() {
    // Verify nonce and capabilities
    if (!wp_verify_nonce($_POST['nonce'], 'redco_webp_stats') || !current_user_can('manage_options')) {
        wp_send_json_error('Security check failed');
        return;
    }

    global $wpdb;

    try {
        // Get all database records claiming WebP conversion
        $conversion_records = $wpdb->get_results("
            SELECT p.ID, pm.meta_value
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'attachment'
            AND pm.meta_key = '_webp_conversion_data'
            AND pm.meta_value LIKE '%converted\";b:1%'
        ");

        $database_records = count($conversion_records);
        $files_found = 0;
        $missing_files = 0;
        $empty_files = 0;
        $missing_file_list = array();

        foreach ($conversion_records as $record) {
            $file_path = get_attached_file($record->ID);
            if (!$file_path) continue;

            // Get WebP path
            $path_info = pathinfo($file_path);
            $webp_path = $path_info['dirname'] . '/' . $path_info['filename'] . '.webp';

            if (file_exists($webp_path)) {
                $file_size = filesize($webp_path);
                if ($file_size > 0) {
                    $files_found++;
                } else {
                    $empty_files++;
                    $missing_file_list[] = basename($webp_path) . ' (empty)';
                }
            } else {
                $missing_files++;
                $missing_file_list[] = basename($webp_path) . ' (missing)';
            }
        }

        wp_send_json_success(array(
            'database_records' => $database_records,
            'files_found' => $files_found,
            'missing_files' => $missing_files,
            'empty_files' => $empty_files,
            'missing_file_list' => array_slice($missing_file_list, 0, 20) // Limit to first 20
        ));

    } catch (Exception $e) {
        wp_send_json_error('Verification failed: ' . $e->getMessage());
    }
}

// Global bulk convert AJAX handler (works regardless of module status)
function redco_webp_ajax_bulk_convert() {
    // Debug logging
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('🔧 Global WebP Bulk Convert Handler Called');
        error_log('🔧 POST data: ' . print_r($_POST, true));
    }

    // Verify nonce and capabilities
    if (!wp_verify_nonce($_POST['nonce'], 'redco_webp_bulk_convert') || !current_user_can('manage_options')) {
        wp_send_json_error(array('message' => 'Security check failed'));
        return;
    }

    // Check if module is enabled
    if (!redco_is_module_enabled('smart-webp-conversion')) {
        wp_send_json_error(array('message' => 'WebP conversion module is not enabled'));
        return;
    }

    // Get module instance
    global $redco_webp_instance;
    if (!$redco_webp_instance) {
        // Create temporary instance for conversion
        if (class_exists('Redco_Smart_WebP_Conversion')) {
            $redco_webp_instance = new Redco_Smart_WebP_Conversion();
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('✅ Created temporary WebP instance for conversion');
            }
        } else {
            wp_send_json_error(array('message' => 'WebP conversion class not available'));
            return;
        }
    }

    // Verify the instance has the required method
    if (!method_exists($redco_webp_instance, 'ajax_bulk_convert')) {
        wp_send_json_error(array('message' => 'Bulk convert method not available'));
        return;
    }

    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('🔧 Calling instance ajax_bulk_convert method');
    }

    // Call the instance method
    try {
        $redco_webp_instance->ajax_bulk_convert();
    } catch (Exception $e) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('❌ Instance method failed: ' . $e->getMessage());
        }
        wp_send_json_error(array('message' => 'Conversion failed: ' . $e->getMessage()));
    }
}

// Global stats AJAX handler (works regardless of module status)
function redco_webp_ajax_get_stats() {
    if (!wp_verify_nonce($_POST['nonce'], 'redco_webp_stats') && !wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    // Check if module is enabled
    if (!redco_is_module_enabled('smart-webp-conversion')) {
        // Return default stats when module is disabled
        wp_send_json(array(
            'total_images' => 0,
            'converted_images' => 0,
            'unconverted_images' => 0,
            'conversion_percentage' => 0,
            'total_original_size' => 0,
            'total_webp_size' => 0,
            'total_savings' => 0,
            'savings_percentage' => 0,
            'recent_conversions' => array(),
            'server_support' => function_exists('imagewebp') && (imagetypes() & IMG_WEBP),
            'browser_support' => false
        ));
        return;
    }

    // Get module instance
    global $redco_webp_instance;
    if (!$redco_webp_instance) {
        if (class_exists('Redco_Smart_WebP_Conversion')) {
            $redco_webp_instance = new Redco_Smart_WebP_Conversion();
        } else {
            wp_send_json_error('WebP conversion class not available');
            return;
        }
    }

    // Call the instance method
    wp_send_json($redco_webp_instance->get_stats());
}

// Global test conversion AJAX handler (works regardless of module status)
function redco_webp_ajax_test_conversion() {
    if (!wp_verify_nonce($_POST['nonce'], 'redco_webp_test') && !wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    $result = array(
        'success' => false,
        'message' => '',
        'capabilities' => array()
    );

    // Test WebP support
    $webp_support = function_exists('imagewebp') && (imagetypes() & IMG_WEBP);
    $result['capabilities']['webp_support'] = $webp_support;
    $result['capabilities']['gd_version'] = function_exists('gd_info') ? gd_info()['GD Version'] : 'Not available';
    $result['capabilities']['supported_formats'] = array();

    if (function_exists('imagetypes')) {
        $types = imagetypes();
        $result['capabilities']['supported_formats']['jpeg'] = (bool)($types & IMG_JPG);
        $result['capabilities']['supported_formats']['png'] = (bool)($types & IMG_PNG);
        $result['capabilities']['supported_formats']['gif'] = (bool)($types & IMG_GIF);
        $result['capabilities']['supported_formats']['webp'] = (bool)($types & IMG_WEBP);
    }

    if ($webp_support) {
        $result['success'] = true;
        $result['message'] = __('Server supports WebP conversion', 'redco-optimizer');
    } else {
        $result['message'] = __('Server does not support WebP conversion. Please contact your hosting provider.', 'redco-optimizer');
    }

    wp_send_json($result);
}

// Test image detection AJAX handler
function redco_webp_ajax_test_image_detection() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    global $wpdb;

    // Test the exact query used in get_unconverted_images - CRITICAL FIX: Use correct serialized boolean pattern
    $query = "
        SELECT p.ID, p.post_title, p.post_mime_type
        FROM {$wpdb->posts} p
        LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_webp_conversion_data'
        WHERE p.post_type = 'attachment'
        AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif')
        AND (
            pm.meta_value IS NULL
            OR pm.meta_value NOT LIKE '%converted\";b:1;%'
            OR pm.meta_value NOT LIKE '%\"converted\";b:1;%'
            OR pm.meta_value = ''
        )
        ORDER BY p.post_date DESC
        LIMIT 10
    ";

    $unconverted_images = $wpdb->get_results($query);

    // Also get all images for comparison
    $all_images_query = "
        SELECT p.ID, p.post_title, p.post_mime_type
        FROM {$wpdb->posts} p
        WHERE p.post_type = 'attachment'
        AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif')
        ORDER BY p.post_date DESC
    ";

    $all_images = $wpdb->get_results($all_images_query);

    // Check for existing conversion data
    $conversion_data_query = "
        SELECT p.ID, pm.meta_value
        FROM {$wpdb->posts} p
        INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
        WHERE p.post_type = 'attachment'
        AND pm.meta_key = '_webp_conversion_data'
    ";

    $existing_conversions = $wpdb->get_results($conversion_data_query);

    wp_send_json_success(array(
        'message' => 'Image detection test completed',
        'all_images_count' => count($all_images),
        'unconverted_images_count' => count($unconverted_images),
        'existing_conversions_count' => count($existing_conversions),
        'all_images' => $all_images,
        'unconverted_images' => $unconverted_images,
        'existing_conversions' => $existing_conversions,
        'query_used' => $query
    ));
}

// Simple test AJAX handler (no nonce required for testing)
function redco_webp_ajax_test_simple() {
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('🔧 Simple test AJAX called');
        error_log('🔧 POST data: ' . print_r($_POST, true));
    }

    wp_send_json_success(array(
        'message' => 'Simple AJAX test successful',
        'timestamp' => current_time('mysql'),
        'user_id' => get_current_user_id()
    ));
}

// Test bulk conversion with detailed logging
function redco_webp_ajax_test_bulk_conversion() {
    // Basic security check
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    // Enable debug logging for this test
    $original_debug = defined('WP_DEBUG') ? WP_DEBUG : false;
    if (!defined('WP_DEBUG')) {
        define('WP_DEBUG', true);
    }

    $debug_log = array();
    $debug_log[] = '=== BULK CONVERSION TEST START ===';

    try {
        // Get module instance
        global $redco_webp_instance;
        if (!$redco_webp_instance && class_exists('Redco_Smart_WebP_Conversion')) {
            $redco_webp_instance = new Redco_Smart_WebP_Conversion();
            $debug_log[] = 'Created WebP instance';
        }

        if (!$redco_webp_instance) {
            wp_send_json_error('WebP instance not available');
            return;
        }

        // Get unconverted images
        $unconverted_images = $redco_webp_instance->get_unconverted_images(10, 0);
        $debug_log[] = 'Found ' . count($unconverted_images) . ' unconverted images';

        foreach ($unconverted_images as $img) {
            $debug_log[] = 'Unconverted image: ID ' . $img->ID . ' - ' . $img->post_title;
        }

        if (empty($unconverted_images)) {
            $debug_log[] = 'No images to convert - this explains why bulk conversion shows 0 processed';
            wp_send_json_success(array(
                'message' => 'No images need conversion',
                'debug_log' => $debug_log,
                'unconverted_count' => 0
            ));
            return;
        }

        // Test converting the first image
        $test_image = $unconverted_images[0];
        $file_path = get_attached_file($test_image->ID);
        $debug_log[] = 'Testing conversion of image ID ' . $test_image->ID . ': ' . basename($file_path);

        if (!file_exists($file_path)) {
            $debug_log[] = 'ERROR: Source file does not exist: ' . $file_path;
            wp_send_json_error(array('debug_log' => $debug_log));
            return;
        }

        $debug_log[] = 'Source file exists and is readable: ' . (is_readable($file_path) ? 'Yes' : 'No');

        // Get settings
        $settings = $redco_webp_instance->get_settings();
        $debug_log[] = 'WebP quality setting: ' . $settings['quality'];

        // Attempt conversion
        try {
            $webp_path = $redco_webp_instance->convert_to_webp($file_path, $settings);
            $debug_log[] = 'Conversion returned path: ' . $webp_path;

            if (file_exists($webp_path)) {
                $debug_log[] = 'WebP file created successfully: ' . filesize($webp_path) . ' bytes';

                // Store conversion data
                $conversion_data = array(
                    'converted' => true,
                    'conversion_date' => current_time('mysql'),
                    'original_size' => filesize($file_path),
                    'webp_size' => filesize($webp_path),
                    'quality' => $settings['quality'],
                    'savings' => filesize($file_path) - filesize($webp_path)
                );

                update_post_meta($test_image->ID, '_webp_conversion_data', $conversion_data);
                $debug_log[] = 'Conversion data saved to database';

                wp_send_json_success(array(
                    'message' => 'Test conversion successful',
                    'debug_log' => $debug_log,
                    'converted_image_id' => $test_image->ID,
                    'webp_path' => basename($webp_path),
                    'original_size' => filesize($file_path),
                    'webp_size' => filesize($webp_path)
                ));
            } else {
                $debug_log[] = 'ERROR: WebP file was not created despite no exception';
                wp_send_json_error(array('debug_log' => $debug_log));
            }

        } catch (Exception $e) {
            $debug_log[] = 'CONVERSION ERROR: ' . $e->getMessage();
            wp_send_json_error(array('debug_log' => $debug_log, 'error' => $e->getMessage()));
        }

    } catch (Exception $e) {
        $debug_log[] = 'GENERAL ERROR: ' . $e->getMessage();
        wp_send_json_error(array('debug_log' => $debug_log, 'error' => $e->getMessage()));
    }
}

// Quick debug AJAX handler (minimal security for testing)
function redco_webp_ajax_quick_debug() {
    // Basic security check
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    global $wpdb;

    $debug_data = array();

    // Get all images with detailed info
    $all_images = $wpdb->get_results("
        SELECT p.ID, p.post_title, p.post_mime_type
        FROM {$wpdb->posts} p
        WHERE p.post_type = 'attachment'
        AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif')
        ORDER BY p.post_date DESC
    ");

    $debug_data['total_images'] = count($all_images);
    $debug_data['all_image_details'] = array();

    // Detailed analysis of each image
    foreach ($all_images as $img) {
        $file_path = get_attached_file($img->ID);
        $conversion_data = get_post_meta($img->ID, '_webp_conversion_data', true);
        $is_converted = is_array($conversion_data) && isset($conversion_data['converted']) && $conversion_data['converted'] === true;

        // Check WebP file existence
        $webp_path = '';
        $webp_exists = false;
        if (class_exists('Redco_Smart_WebP_Conversion')) {
            $webp_instance = new Redco_Smart_WebP_Conversion();
            try {
                $reflection = new ReflectionClass($webp_instance);
                $method = $reflection->getMethod('get_webp_path');
                $method->setAccessible(true);
                $webp_path = $method->invoke($webp_instance, $file_path);
                $webp_exists = file_exists($webp_path);
            } catch (Exception $e) {
                $webp_path = 'Error: ' . $e->getMessage();
            }
        }

        $debug_data['all_image_details'][] = array(
            'id' => $img->ID,
            'title' => $img->post_title,
            'mime_type' => $img->post_mime_type,
            'file_path' => basename($file_path),
            'file_exists' => file_exists($file_path),
            'file_readable' => is_readable($file_path),
            'webp_path' => basename($webp_path),
            'webp_exists' => $webp_exists,
            'webp_size' => $webp_exists ? filesize($webp_path) : 0,
            'has_conversion_data' => !empty($conversion_data),
            'is_converted' => $is_converted,
            'conversion_data' => $conversion_data,
            'REAL_STATUS' => $webp_exists ? 'ACTUALLY_CONVERTED' : 'NOT_CONVERTED'
        );
    }

    // Get conversion data from database
    $conversion_data = $wpdb->get_results("
        SELECT p.ID, pm.meta_value
        FROM {$wpdb->posts} p
        INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
        WHERE p.post_type = 'attachment'
        AND pm.meta_key = '_webp_conversion_data'
    ");

    $debug_data['conversion_records'] = count($conversion_data);

    // Programmatic check
    $converted_count = 0;
    $unconverted_count = 0;

    foreach ($all_images as $img) {
        $conversion_data = get_post_meta($img->ID, '_webp_conversion_data', true);
        $is_converted = is_array($conversion_data) && isset($conversion_data['converted']) && $conversion_data['converted'] === true;

        if ($is_converted) {
            $converted_count++;
        } else {
            $unconverted_count++;
        }
    }

    $debug_data['programmatic_summary'] = array(
        'total' => count($all_images),
        'converted' => $converted_count,
        'unconverted' => $unconverted_count
    );

    // Test get_unconverted_images method
    if (class_exists('Redco_Smart_WebP_Conversion')) {
        $webp_instance = new Redco_Smart_WebP_Conversion();
        if (method_exists($webp_instance, 'get_unconverted_images')) {
            $unconverted_images = $webp_instance->get_unconverted_images(20, 0);
            $debug_data['unconverted_method_result'] = array(
                'count' => count($unconverted_images),
                'image_ids' => array_map(function($img) { return $img->ID; }, $unconverted_images)
            );
        }

        // Current stats
        if (method_exists($webp_instance, 'get_stats')) {
            $stats = $webp_instance->get_stats();
            $debug_data['current_stats'] = array(
                'total_images' => $stats['total_images'],
                'converted_images' => $stats['converted_images'],
                'unconverted_images' => $stats['unconverted_images']
            );
        }
    }

    wp_send_json_success($debug_data);
}

// Always enqueue scripts for admin interface
if (is_admin()) {
    add_action('admin_enqueue_scripts', 'redco_webp_enqueue_admin_scripts');
    add_action('wp_ajax_redco_webp_debug_images', 'redco_webp_ajax_debug_images');
    add_action('wp_ajax_redco_webp_test_simple', 'redco_webp_ajax_test_simple');
    add_action('wp_ajax_redco_webp_test_bulk_conversion', 'redco_webp_ajax_test_bulk_conversion');
    add_action('wp_ajax_redco_webp_quick_debug', 'redco_webp_ajax_quick_debug');
    add_action('wp_ajax_redco_webp_bulk_convert', 'redco_webp_ajax_bulk_convert');
    add_action('wp_ajax_redco_webp_get_stats', 'redco_webp_ajax_get_stats');
    add_action('wp_ajax_redco_webp_test_conversion', 'redco_webp_ajax_test_conversion');
    add_action('wp_ajax_redco_webp_test_image_detection', 'redco_webp_ajax_test_image_detection');
    add_action('wp_ajax_redco_webp_verify_files', 'redco_webp_ajax_verify_files');
    add_action('wp_ajax_redco_webp_system_diagnostics', 'redco_webp_ajax_system_diagnostics');
    add_action('wp_ajax_redco_webp_repair_data', 'redco_webp_ajax_repair_data');
    add_action('wp_ajax_redco_webp_debug_analysis', 'redco_webp_ajax_debug_analysis');
    add_action('wp_ajax_redco_webp_reset_conversions', 'redco_webp_ajax_reset_conversions');
    add_action('wp_ajax_redco_webp_reset_specific_images', 'redco_webp_ajax_reset_specific_images');
    add_action('wp_ajax_redco_webp_test_creation', 'redco_webp_ajax_test_creation');
}

// Initialize the module only if enabled and after init hook
function redco_init_smart_webp_conversion() {
    if (redco_is_module_enabled('smart-webp-conversion')) {
        global $redco_webp_instance;
        $redco_webp_instance = new Redco_Smart_WebP_Conversion();
    }
}
add_action('init', 'redco_init_smart_webp_conversion', 10);
