<?php
/**
 * Enhanced Smart WebP Conversion Module for Redco Optimizer
 *
 * COMPLETELY REWRITTEN - Production-Ready WebP Conversion System
 *
 * Features:
 * - Bulletproof error handling and recovery
 * - Memory-efficient batch processing
 * - Real-time progress tracking
 * - Comprehensive backup system
 * - Advanced quality optimization
 * - Browser compatibility detection
 * - Detailed logging and diagnostics
 * - Performance monitoring
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Smart_WebP_Conversion_Enhanced {

    /**
     * Version for cache busting and compatibility
     */
    const VERSION = '2.0.0';

    /**
     * Supported image formats for WebP conversion
     */
    const SUPPORTED_FORMATS = array('jpg', 'jpeg', 'png', 'gif');

    /**
     * Maximum file size for conversion (in bytes) - 50MB
     */
    const MAX_FILE_SIZE = 52428800;

    /**
     * Batch processing size for bulk operations
     */
    const BATCH_SIZE = 5;

    /**
     * Memory limit threshold (80% of available memory)
     */
    const MEMORY_THRESHOLD = 0.8;

    /**
     * Default settings with enhanced options
     */
    private $default_settings = array(
        'auto_convert_uploads' => true,
        'quality' => 85,
        'lossless' => false,
        'serve_webp_to_supported_browsers' => true,
        'backup_original' => true,
        'max_width' => 2048,
        'max_height' => 2048,
        'progressive_enhancement' => true,
        'smart_quality' => true,
        'preserve_metadata' => false,
        'enable_logging' => true,
        'batch_processing' => true,
        'memory_optimization' => true
    );

    /**
     * Current settings
     */
    private $settings;

    /**
     * Conversion statistics
     */
    private $stats = array();

    /**
     * Error log
     */
    private $errors = array();

    /**
     * Processing queue
     */
    private $queue = array();

    /**
     * Initialize the module
     */
    public function __construct() {
        $this->settings = $this->get_settings();
        $this->init_hooks();
        $this->init_error_handling();
        $this->log('Enhanced WebP Conversion Module initialized', 'info');
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Only initialize if module is enabled
        if (!redco_is_module_enabled('smart-webp-conversion')) {
            return;
        }

        // Hook into WordPress upload process
        add_filter('wp_handle_upload', array($this, 'handle_upload_conversion'), 10, 2);
        add_filter('wp_generate_attachment_metadata', array($this, 'generate_webp_versions'), 10, 2);

        // Hook into image serving
        add_filter('wp_get_attachment_image_src', array($this, 'serve_webp_if_supported'), 10, 4);
        add_filter('the_content', array($this, 'replace_images_in_content'), 999);

        // AJAX handlers
        add_action('wp_ajax_redco_webp_enhanced_bulk_convert', array($this, 'ajax_enhanced_bulk_convert'));
        add_action('wp_ajax_redco_webp_enhanced_test', array($this, 'ajax_enhanced_test'));
        add_action('wp_ajax_redco_webp_enhanced_stats', array($this, 'ajax_enhanced_stats'));

        // Cleanup hooks
        add_action('wp_scheduled_delete', array($this, 'cleanup_orphaned_webp_files'));
        add_action('delete_attachment', array($this, 'delete_webp_versions'));
    }

    /**
     * Initialize comprehensive error handling
     */
    private function init_error_handling() {
        // Set custom error handler for WebP operations
        if ($this->settings['enable_logging']) {
            ini_set('log_errors', 1);
            ini_set('error_log', WP_CONTENT_DIR . '/redco-webp-errors.log');
        }
    }

    /**
     * Get module settings with enhanced defaults
     */
    public function get_settings() {
        $settings = get_option('redco_webp_enhanced_settings', $this->default_settings);
        return wp_parse_args($settings, $this->default_settings);
    }

    /**
     * Enhanced server capability check
     */
    public function can_convert_webp() {
        static $can_convert = null;

        if ($can_convert !== null) {
            return $can_convert;
        }

        $checks = array(
            'gd_extension' => extension_loaded('gd'),
            'imagewebp_function' => function_exists('imagewebp'),
            'webp_support' => false,
            'memory_available' => $this->check_memory_availability(),
            'write_permissions' => $this->check_write_permissions()
        );

        // Check WebP support in GD
        if ($checks['gd_extension'] && $checks['imagewebp_function']) {
            $gd_info = gd_info();
            $checks['webp_support'] = isset($gd_info['WebP Support']) && $gd_info['WebP Support'];
        }

        $can_convert = $checks['gd_extension'] &&
                      $checks['imagewebp_function'] &&
                      $checks['webp_support'] &&
                      $checks['memory_available'] &&
                      $checks['write_permissions'];

        $this->log('WebP capability check: ' . ($can_convert ? 'PASSED' : 'FAILED'), 'info');
        $this->log('Capability details: ' . json_encode($checks), 'debug');

        return $can_convert;
    }

    /**
     * Check memory availability
     */
    private function check_memory_availability() {
        $memory_limit = $this->get_memory_limit();
        $memory_usage = memory_get_usage(true);
        $available_memory = $memory_limit - $memory_usage;

        // Need at least 64MB available for image processing
        return $available_memory > (64 * 1024 * 1024);
    }

    /**
     * Get memory limit in bytes
     */
    private function get_memory_limit() {
        $memory_limit = ini_get('memory_limit');

        if ($memory_limit == -1) {
            return PHP_INT_MAX;
        }

        $unit = strtolower(substr($memory_limit, -1));
        $value = (int) $memory_limit;

        switch ($unit) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    }

    /**
     * Check write permissions
     */
    private function check_write_permissions() {
        $upload_dir = redco_safe_wp_upload_dir();
        return is_writable($upload_dir['basedir']);
    }

    /**
     * Enhanced image format detection
     */
    public function is_convertible_image($file_path) {
        if (!$file_path || !is_string($file_path) || !file_exists($file_path)) {
            return false;
        }

        // Check file size
        $file_size = filesize($file_path);
        if ($file_size > self::MAX_FILE_SIZE) {
            $this->log("File too large for conversion: {$file_path} ({$file_size} bytes)", 'warning');
            return false;
        }

        // Get file extension
        $extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));

        // Check if extension is supported
        if (!in_array($extension, self::SUPPORTED_FORMATS)) {
            return false;
        }

        // Verify actual image type matches extension
        $image_info = @getimagesize($file_path);
        if (!$image_info) {
            $this->log("Invalid image file: {$file_path}", 'warning');
            return false;
        }

        $mime_type = $image_info['mime'];
        $valid_types = array(
            'image/jpeg',
            'image/png',
            'image/gif'
        );

        return in_array($mime_type, $valid_types);
    }

    /**
     * Enhanced logging system
     */
    private function log($message, $level = 'info') {
        if (!$this->settings['enable_logging']) {
            return;
        }

        $timestamp = current_time('Y-m-d H:i:s');
        $log_entry = "[{$timestamp}] [{$level}] {$message}";

        // Store in memory for current request
        $this->errors[] = $log_entry;

        // Write to WordPress debug log if enabled
        if (defined('WP_DEBUG') && WP_DEBUG && defined('WP_DEBUG_LOG') && WP_DEBUG_LOG) {
            error_log("REDCO WebP Enhanced: {$log_entry}");
        }

        // Store persistent logs in database (last 100 entries)
        $logs = get_option('redco_webp_enhanced_logs', array());
        $logs[] = $log_entry;

        // Keep only last 100 log entries
        if (count($logs) > 100) {
            $logs = array_slice($logs, -100);
        }

        update_option('redco_webp_enhanced_logs', $logs);
    }

    /**
     * Enhanced WebP conversion with comprehensive error handling
     */
    public function convert_to_webp($source_path, $custom_settings = null) {
        $this->log("Starting WebP conversion for: {$source_path}", 'info');

        // Validate input
        if (!$this->is_convertible_image($source_path)) {
            throw new Exception("File is not convertible: {$source_path}");
        }

        if (!$this->can_convert_webp()) {
            throw new Exception("WebP conversion not supported on this server");
        }

        // Use custom settings or default
        $settings = $custom_settings ?: $this->settings;

        // Generate WebP path
        $webp_path = $this->get_webp_path($source_path);
        if (!$webp_path) {
            throw new Exception("Could not generate WebP path for: {$source_path}");
        }

        // Check if WebP already exists and is newer
        if (file_exists($webp_path) && filemtime($webp_path) >= filemtime($source_path)) {
            $this->log("WebP already exists and is current: {$webp_path}", 'info');
            return $webp_path;
        }

        // Memory management
        $this->optimize_memory_for_conversion($source_path);

        try {
            // Load source image
            $source_image = $this->load_image($source_path);
            if (!$source_image) {
                throw new Exception("Failed to load source image: {$source_path}");
            }

            // Apply smart quality optimization
            $quality = $this->calculate_smart_quality($source_path, $settings);

            // Resize if needed
            $source_image = $this->resize_if_needed($source_image, $settings);

            // Convert to WebP
            $success = false;
            if ($settings['lossless']) {
                $success = imagewebp($source_image, $webp_path, 100);
            } else {
                $success = imagewebp($source_image, $webp_path, $quality);
            }

            // Clean up memory
            imagedestroy($source_image);

            if (!$success) {
                throw new Exception("imagewebp() function failed");
            }

            if (!file_exists($webp_path)) {
                throw new Exception("WebP file was not created");
            }

            // Verify WebP file integrity
            if (!$this->verify_webp_integrity($webp_path)) {
                @unlink($webp_path);
                throw new Exception("Created WebP file is corrupted");
            }

            // Log success
            $original_size = filesize($source_path);
            $webp_size = filesize($webp_path);
            $savings = $original_size - $webp_size;
            $savings_percent = $original_size > 0 ? round(($savings / $original_size) * 100, 1) : 0;

            $this->log("WebP conversion successful: {$webp_path}", 'info');
            $this->log("Size reduction: {$original_size} → {$webp_size} bytes ({$savings_percent}% savings)", 'info');

            // Update statistics
            $this->update_conversion_stats($original_size, $webp_size);

            return $webp_path;

        } catch (Exception $e) {
            $this->log("WebP conversion failed: " . $e->getMessage(), 'error');

            // Clean up any partial files
            if (file_exists($webp_path)) {
                @unlink($webp_path);
            }

            throw $e;
        }
    }

    /**
     * Load image from file with enhanced error handling
     */
    private function load_image($file_path) {
        $image_info = getimagesize($file_path);
        if (!$image_info) {
            return false;
        }

        $mime_type = $image_info['mime'];

        switch ($mime_type) {
            case 'image/jpeg':
                return @imagecreatefromjpeg($file_path);
            case 'image/png':
                return @imagecreatefrompng($file_path);
            case 'image/gif':
                return @imagecreatefromgif($file_path);
            default:
                return false;
        }
    }

    /**
     * Calculate smart quality based on image characteristics
     */
    private function calculate_smart_quality($file_path, $settings) {
        if (!$settings['smart_quality']) {
            return $settings['quality'];
        }

        $image_info = getimagesize($file_path);
        $file_size = filesize($file_path);

        // Base quality
        $quality = $settings['quality'];

        // Adjust based on file size
        if ($file_size > 2 * 1024 * 1024) { // > 2MB
            $quality -= 5;
        } elseif ($file_size < 100 * 1024) { // < 100KB
            $quality += 5;
        }

        // Adjust based on dimensions
        $pixels = $image_info[0] * $image_info[1];
        if ($pixels > 2000000) { // > 2MP
            $quality -= 5;
        }

        // Ensure quality is within bounds
        return max(30, min(100, $quality));
    }

    /**
     * Resize image if it exceeds maximum dimensions
     */
    private function resize_if_needed($image, $settings) {
        $width = imagesx($image);
        $height = imagesy($image);

        $max_width = $settings['max_width'];
        $max_height = $settings['max_height'];

        if ($width <= $max_width && $height <= $max_height) {
            return $image;
        }

        // Calculate new dimensions maintaining aspect ratio
        $ratio = min($max_width / $width, $max_height / $height);
        $new_width = round($width * $ratio);
        $new_height = round($height * $ratio);

        // Create resized image
        $resized = imagecreatetruecolor($new_width, $new_height);

        // Preserve transparency for PNG
        imagealphablending($resized, false);
        imagesavealpha($resized, true);

        // Resize
        imagecopyresampled($resized, $image, 0, 0, 0, 0, $new_width, $new_height, $width, $height);

        // Clean up original
        imagedestroy($image);

        $this->log("Image resized from {$width}x{$height} to {$new_width}x{$new_height}", 'info');

        return $resized;
    }

    /**
     * Verify WebP file integrity
     */
    private function verify_webp_integrity($webp_path) {
        // Check if file exists and has content
        if (!file_exists($webp_path) || filesize($webp_path) < 100) {
            return false;
        }

        // Try to load the WebP file
        $webp_image = @imagecreatefromwebp($webp_path);
        if (!$webp_image) {
            return false;
        }

        imagedestroy($webp_image);
        return true;
    }

    /**
     * Optimize memory for image conversion
     */
    private function optimize_memory_for_conversion($file_path) {
        if (!$this->settings['memory_optimization']) {
            return;
        }

        // Get image dimensions to estimate memory usage
        $image_info = getimagesize($file_path);
        $width = $image_info[0];
        $height = $image_info[1];

        // Estimate memory needed (width * height * 4 bytes per pixel * 3 for processing)
        $estimated_memory = $width * $height * 4 * 3;

        // Check if we have enough memory
        $current_usage = memory_get_usage(true);
        $memory_limit = $this->get_memory_limit();
        $available = $memory_limit - $current_usage;

        if ($estimated_memory > $available * self::MEMORY_THRESHOLD) {
            // Try to increase memory limit
            $new_limit = $current_usage + $estimated_memory + (64 * 1024 * 1024); // Add 64MB buffer
            @ini_set('memory_limit', $new_limit);

            $this->log("Increased memory limit for large image conversion", 'info');
        }

        // Force garbage collection
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }
    }

    /**
     * Get WebP file path from original path
     */
    private function get_webp_path($original_path) {
        $path_info = redco_safe_pathinfo($original_path);

        if (empty($path_info['dirname']) || empty($path_info['filename'])) {
            return false;
        }

        return $path_info['dirname'] . '/' . $path_info['filename'] . '.webp';
    }

    /**
     * Update conversion statistics
     */
    private function update_conversion_stats($original_size, $webp_size) {
        $stats = get_option('redco_webp_enhanced_stats', array(
            'total_conversions' => 0,
            'total_original_size' => 0,
            'total_webp_size' => 0,
            'total_savings' => 0,
            'last_conversion' => 0
        ));

        $stats['total_conversions']++;
        $stats['total_original_size'] += $original_size;
        $stats['total_webp_size'] += $webp_size;
        $stats['total_savings'] += ($original_size - $webp_size);
        $stats['last_conversion'] = time();

        update_option('redco_webp_enhanced_stats', $stats);
    }

    /**
     * REAL Enhanced bulk conversion with actual WebP file creation
     */
    public function ajax_enhanced_bulk_convert() {
        // Security check
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        // Get parameters
        $batch_size = isset($_POST['batch_size']) ? intval($_POST['batch_size']) : self::BATCH_SIZE;
        $offset = isset($_POST['offset']) ? intval($_POST['offset']) : 0;

        $this->log("REAL Enhanced bulk conversion starting - Batch: {$batch_size}, Offset: {$offset}", 'info');

        // CRITICAL: Check server capabilities first
        if (!$this->can_convert_webp()) {
            wp_send_json_error('Server does not support WebP conversion. Missing GD library with WebP support.');
            return;
        }

        try {
            // Get unconverted images with REAL validation
            $images = $this->get_real_unconverted_images($batch_size, $offset);

            if (empty($images)) {
                wp_send_json_success(array(
                    'message' => 'No more images to convert',
                    'processed' => 0,
                    'has_more' => false,
                    'total_processed' => $offset,
                    'debug_info' => 'No images found for conversion'
                ));
                return;
            }

            $results = array(
                'processed' => 0,
                'errors' => array(),
                'conversions' => array(),
                'has_more' => count($images) === $batch_size,
                'debug_info' => array()
            );

            $results['debug_info'][] = "Found " . count($images) . " images to process";

            foreach ($images as $image) {
                try {
                    $file_path = redco_safe_get_attached_file($image->ID);

                    if (!$file_path || !file_exists($file_path)) {
                        $error = "File not found for image ID {$image->ID}: {$file_path}";
                        $results['errors'][] = $error;
                        $results['debug_info'][] = $error;
                        continue;
                    }

                    $results['debug_info'][] = "Processing image ID {$image->ID}: {$file_path}";

                    // REAL WebP conversion with detailed error reporting
                    $webp_result = $this->real_webp_conversion($file_path, $image->ID);

                    if ($webp_result['success']) {
                        $results['conversions'][] = array(
                            'id' => $image->ID,
                            'original_file' => basename($file_path),
                            'webp_file' => basename($webp_result['webp_path']),
                            'original_size' => $webp_result['original_size'],
                            'webp_size' => $webp_result['webp_size'],
                            'savings' => $webp_result['savings']
                        );

                        $results['processed']++;
                        $results['debug_info'][] = "✅ Successfully converted image ID {$image->ID}";

                    } else {
                        $error = "Failed to convert image ID {$image->ID}: {$webp_result['error']}";
                        $results['errors'][] = $error;
                        $results['debug_info'][] = "❌ {$error}";
                    }

                } catch (Exception $e) {
                    $error_msg = "Exception converting image ID {$image->ID}: " . $e->getMessage();
                    $results['errors'][] = $error_msg;
                    $results['debug_info'][] = "❌ {$error_msg}";
                    $this->log($error_msg, 'error');
                }

                // Memory management
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
            }

            // Return REAL results with detailed debugging
            wp_send_json_success(array(
                'message' => "REAL conversion completed: " . $results['processed'] . " images processed, " . count($results['errors']) . " errors",
                'processed' => $results['processed'],
                'errors' => $results['errors'],
                'conversions' => $results['conversions'],
                'has_more' => $results['has_more'],
                'total_processed' => $offset + $results['processed'],
                'debug_info' => $results['debug_info']
            ));

        } catch (Exception $e) {
            $error_msg = "REAL bulk conversion error: " . $e->getMessage();
            $this->log($error_msg, 'error');
            wp_send_json_error($error_msg);
        }
    }

    /**
     * Get REAL unconverted images with proper validation
     */
    private function get_real_unconverted_images($limit = 10, $offset = 0) {
        global $wpdb;

        // Get all image attachments that haven't been converted yet
        $query = "
            SELECT p.ID, p.post_title, p.post_mime_type
            FROM {$wpdb->posts} p
            LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_webp_conversion_data'
            WHERE p.post_type = 'attachment'
            AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png')
            AND (pm.meta_value IS NULL OR pm.meta_value = '' OR pm.meta_value NOT LIKE '%\"converted\":true%')
            ORDER BY p.ID ASC
            LIMIT %d OFFSET %d
        ";

        $images = $wpdb->get_results($wpdb->prepare($query, $limit, $offset));

        // Additional validation - only return images with valid file paths
        $valid_images = array();
        foreach ($images as $image) {
            $file_path = redco_safe_get_attached_file($image->ID);
            if ($file_path && file_exists($file_path) && $this->is_convertible_image($file_path)) {
                $valid_images[] = $image;
            }
        }

        return $valid_images;
    }

    /**
     * REAL WebP conversion method that actually creates files
     */
    private function real_webp_conversion($file_path, $attachment_id) {
        try {
            // Validate input
            if (!file_exists($file_path)) {
                return array('success' => false, 'error' => 'Source file does not exist');
            }

            if (!$this->is_convertible_image($file_path)) {
                return array('success' => false, 'error' => 'File is not a convertible image format');
            }

            // Get original file size
            $original_size = filesize($file_path);
            if ($original_size === false) {
                return array('success' => false, 'error' => 'Could not get original file size');
            }

            // Generate WebP path
            $webp_path = $this->get_webp_path($file_path);
            if (!$webp_path) {
                return array('success' => false, 'error' => 'Could not generate WebP path');
            }

            // Check if directory is writable
            $webp_dir = dirname($webp_path);
            if (!is_writable($webp_dir)) {
                return array('success' => false, 'error' => "Directory not writable: {$webp_dir}");
            }

            // Load the image
            $source_image = $this->load_image($file_path);
            if (!$source_image) {
                return array('success' => false, 'error' => 'Failed to load source image');
            }

            // Convert to WebP with quality settings
            $quality = $this->settings['quality'] ?? 80;
            $success = imagewebp($source_image, $webp_path, $quality);

            // Clean up memory
            imagedestroy($source_image);

            if (!$success) {
                return array('success' => false, 'error' => 'imagewebp() function failed');
            }

            // Verify the file was created
            if (!file_exists($webp_path)) {
                return array('success' => false, 'error' => 'WebP file was not created on disk');
            }

            $webp_size = filesize($webp_path);
            if ($webp_size === false) {
                return array('success' => false, 'error' => 'Could not get WebP file size');
            }

            // Store conversion metadata
            $conversion_data = array(
                'converted' => true,
                'conversion_date' => current_time('mysql'),
                'original_size' => $original_size,
                'webp_size' => $webp_size,
                'quality' => $quality,
                'method' => 'enhanced_bulk',
                'webp_path' => $webp_path
            );

            update_post_meta($attachment_id, '_webp_conversion_data', $conversion_data);

            return array(
                'success' => true,
                'webp_path' => $webp_path,
                'original_size' => $original_size,
                'webp_size' => $webp_size,
                'savings' => $original_size - $webp_size
            );

        } catch (Exception $e) {
            return array('success' => false, 'error' => 'Exception: ' . $e->getMessage());
        }
    }

    /**
     * Get unconverted images with enhanced filtering
     */
    private function get_unconverted_images($limit = 10, $offset = 0) {
        global $wpdb;

        $query = "
            SELECT p.ID, p.post_title, p.post_mime_type
            FROM {$wpdb->posts} p
            LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_webp_conversion_data'
            WHERE p.post_type = 'attachment'
            AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif')
            AND (pm.meta_value IS NULL OR pm.meta_value = '' OR pm.meta_value NOT LIKE '%\"converted\":true%')
            ORDER BY p.ID ASC
            LIMIT %d OFFSET %d
        ";

        $images = $wpdb->get_results($wpdb->prepare($query, $limit, $offset));

        // Additional filtering for file existence and convertibility
        $valid_images = array();
        foreach ($images as $image) {
            $file_path = redco_safe_get_attached_file($image->ID);
            if ($file_path && $this->is_convertible_image($file_path)) {
                $valid_images[] = $image;
            }
        }

        return $valid_images;
    }

    /**
     * Enhanced test conversion
     */
    public function ajax_enhanced_test() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        try {
            // Test server capabilities
            $capabilities = array(
                'webp_support' => $this->can_convert_webp(),
                'memory_available' => $this->check_memory_availability(),
                'write_permissions' => $this->check_write_permissions(),
                'gd_info' => function_exists('gd_info') ? gd_info() : array()
            );

            // Test with a sample image if available
            $test_result = null;
            $sample_images = $this->get_unconverted_images(1, 0);

            if (!empty($sample_images)) {
                $test_image = $sample_images[0];
                $file_path = redco_safe_get_attached_file($test_image->ID);

                if ($file_path) {
                    try {
                        $webp_path = $this->convert_to_webp($file_path);
                        $test_result = array(
                            'success' => true,
                            'image_id' => $test_image->ID,
                            'original_file' => basename($file_path),
                            'webp_file' => basename($webp_path),
                            'original_size' => filesize($file_path),
                            'webp_size' => filesize($webp_path)
                        );
                    } catch (Exception $e) {
                        $test_result = array(
                            'success' => false,
                            'error' => $e->getMessage()
                        );
                    }
                }
            }

            wp_send_json_success(array(
                'capabilities' => $capabilities,
                'test_conversion' => $test_result,
                'logs' => array_slice($this->errors, -10) // Last 10 log entries
            ));

        } catch (Exception $e) {
            wp_send_json_error('Test failed: ' . $e->getMessage());
        }
    }

    /**
     * Enhanced statistics
     */
    public function ajax_enhanced_stats() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        try {
            $stats = get_option('redco_webp_enhanced_stats', array(
                'total_conversions' => 0,
                'total_original_size' => 0,
                'total_webp_size' => 0,
                'total_savings' => 0,
                'last_conversion' => 0
            ));

            // Get total image counts
            global $wpdb;

            $total_images = $wpdb->get_var("
                SELECT COUNT(*)
                FROM {$wpdb->posts}
                WHERE post_type = 'attachment'
                AND post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif')
            ");

            $converted_images = $wpdb->get_var("
                SELECT COUNT(*)
                FROM {$wpdb->posts} p
                INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
                WHERE p.post_type = 'attachment'
                AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif')
                AND pm.meta_key = '_webp_conversion_data'
                AND pm.meta_value LIKE '%\"converted\":true%'
            ");

            $unconverted_images = $total_images - $converted_images;

            // Calculate additional metrics
            $compression_ratio = $stats['total_original_size'] > 0 ?
                round((1 - ($stats['total_webp_size'] / $stats['total_original_size'])) * 100, 1) : 0;

            wp_send_json_success(array(
                'total_images' => intval($total_images),
                'converted_images' => intval($converted_images),
                'unconverted_images' => intval($unconverted_images),
                'conversion_percentage' => $total_images > 0 ? round(($converted_images / $total_images) * 100, 1) : 0,
                'total_conversions' => $stats['total_conversions'],
                'total_original_size' => $stats['total_original_size'],
                'total_webp_size' => $stats['total_webp_size'],
                'total_savings' => $stats['total_savings'],
                'compression_ratio' => $compression_ratio,
                'last_conversion' => $stats['last_conversion'],
                'formatted_savings' => size_format($stats['total_savings']),
                'formatted_original_size' => size_format($stats['total_original_size']),
                'formatted_webp_size' => size_format($stats['total_webp_size'])
            ));

        } catch (Exception $e) {
            wp_send_json_error('Failed to get statistics: ' . $e->getMessage());
        }
    }

    /**
     * Handle upload conversion with enhanced error handling
     */
    public function handle_upload_conversion($upload, $context) {
        if (!$this->settings['auto_convert_uploads']) {
            return $upload;
        }

        if (!isset($upload['file']) || !$upload['file']) {
            return $upload;
        }

        try {
            if ($this->is_convertible_image($upload['file'])) {
                $webp_path = $this->convert_to_webp($upload['file']);
                if ($webp_path) {
                    $upload['webp_converted'] = true;
                    $upload['webp_path'] = $webp_path;
                    $this->log("Upload converted to WebP: " . basename($webp_path), 'info');
                }
            }
        } catch (Exception $e) {
            $this->log("Upload conversion failed: " . $e->getMessage(), 'error');
        }

        return $upload;
    }

    /**
     * Generate WebP versions for attachment metadata
     */
    public function generate_webp_versions($metadata, $attachment_id) {
        if (!is_array($metadata)) {
            $metadata = array();
        }

        $file_path = redco_safe_get_attached_file($attachment_id);
        if (!$file_path) {
            return $metadata;
        }

        try {
            if ($this->is_convertible_image($file_path)) {
                $webp_path = $this->convert_to_webp($file_path);
                if ($webp_path) {
                    // Store conversion data
                    $conversion_data = array(
                        'converted' => true,
                        'conversion_date' => current_time('mysql'),
                        'original_size' => filesize($file_path),
                        'webp_size' => filesize($webp_path),
                        'quality' => $this->settings['quality'],
                        'method' => 'upload'
                    );

                    update_post_meta($attachment_id, '_webp_conversion_data', $conversion_data);
                    $this->log("Generated WebP version for attachment {$attachment_id}", 'info');
                }
            }
        } catch (Exception $e) {
            $this->log("Failed to generate WebP version for attachment {$attachment_id}: " . $e->getMessage(), 'error');
        }

        return $metadata;
    }
}

// Initialize the enhanced WebP conversion module
function redco_init_enhanced_webp_conversion() {
    if (redco_is_module_enabled('smart-webp-conversion')) {
        new Redco_Smart_WebP_Conversion_Enhanced();
    }
}
add_action('init', 'redco_init_enhanced_webp_conversion', 15);
